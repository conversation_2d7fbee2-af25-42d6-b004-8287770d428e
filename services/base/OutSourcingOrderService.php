<?php

namespace app\services\base;

use app\commands\OutSourcingController;
use app\libs\BusinessException;
use app\libs\DateHelper;
use app\models\backyard\AuditApply;
use app\models\backyard\HcmExcelTask;
use app\models\backyard\HrOutsourcingOrder;
use app\models\backyard\HrOutsourcingOrderDetail;
use app\models\backyard\HrShift;
use app\models\backyard\HrStaffOutsourcing;
use app\models\backyard\HrStaffShift;
use app\models\backyard\HrStaffShiftMiddleDate;
use app\models\backyard\SettingEnv;
use app\models\fle\SysStore;
use app\models\manage\BaseStaffInfo;
use app\models\manage\ExportManage;
use app\models\manage\HrOperateLogs;
use app\models\manage\HrStaffInfoPosition;
use app\models\manage\StaffInfo;
use app\modules\v1\business\OutsourcingOrder;
use app\modules\v1\business\Staff;
use Yii;
use yii\base\InvalidConfigException;
use yii\db\Query;

class OutSourcingOrderService extends BaseService
{
    //车辆类型
    public static $car_type = [
        13   => 'Bike',
        110  => 'Van',
        452  => 'Boat',
        98   => 'Bike',
        1194 => 'Truck',
        1199 => 'Car',
    ];

    /**
     * 立即生效 创建外协工号
     * @param $serial_no
     * @param $detail_value
     * @param $fbid
     * @return array|mixed|string|string[]|true
     */
    public function effective_staff($serial_no, $detail_value, $fbid)
    {
        $order         = HrOutsourcingOrder::find()->where(['serial_no' => $serial_no])->asArray()->one();
        $job_id        = $order['job_id'];
        $store_id      = $order['store_id'];
        $department_id = $order['department_id'];
        $os_type       = $order['os_type'];

        $staff_job_position = OutSourcingService::$job_position_country[YII_COUNTRY] ?? OutSourcingService::$job_position_country['TH'];

        $staff_arr = [
            'name'               => $detail_value['staff_name'],
            'sex'                => $detail_value['sex'],
            'identity'           => $detail_value['identity'],
            'mobile'             => $detail_value['mobile'],
            'personal_email'     => $detail_value['personal_email'] ?? '',
            'job_title'          => $job_id, //职位id
            'sys_store_id'       => $store_id, //网点id
            'sys_department_id'  => $department_id, //部门id
            'node_department_id' => $department_id,
            'formal'             => BaseStaffInfo::FORMAL_OUTSOURCE,
            'state'              => BaseStaffInfo::STATE_ON_JOB,
            'hire_date'          => date('Y-m-d', strtotime($order['effective_date'])),
            'bank_no'            => $detail_value['bank_no'],
            'bank_type'          => $detail_value['bank_id'],
            'outsourcing_type'   => $detail_value['outsourcing_type'],
            'bank_no_name'       => $detail_value['bank_no_name'],
            'staff_car_no'       => $detail_value['car_no'],
            'company_name_ef'    => $detail_value['company_name_ef'],
            'pay_type'           => $detail_value['pay_type'],
            'position_category'  => $staff_job_position[$job_id] ?? [],
            'staff_car_type'     => self::$car_type[$job_id] ?? '',
            'driver_license'     => $detail_value['driver_license'],
            'staff_type'         => $os_type == 1 ? BaseStaffInfo::STAFF_TYPE_OS_ORDER_SHORT : BaseStaffInfo::STAFF_TYPE_OS_ORDER_LONG,
            'leave_date'         => null,
//            'hire_type'          => $order['hire_os_type'] == BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING ? BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING : BaseStaffInfo::HIRE_OS_TYPE_COMMON,
            'hire_type'          => $order['hire_os_type'] ?? BaseStaffInfo::HIRE_OS_TYPE_COMMON,//雇佣类型 根据订单的雇佣类型 默认普通 新增了 15 兼职外协
            'relatives_call_name'    => $detail_value['relatives_call_name'],
            'relatives_mobile'       => $detail_value['relatives_mobile'],
            'relatives_relationship' => $detail_value['relatives_relationship'],
            'residential_address'    => $detail_value['residential_address'],
        ];

        $base_staff_info = BaseStaffInfo::find()
            ->where(['identity' => $detail_value['identity']])
            ->andWhere(['formal' => BaseStaffInfo::FORMAL_OUTSOURCE])
            ->andWhere(['staff_type' => [BaseStaffInfo::STAFF_TYPE_OS_ORDER_SHORT, BaseStaffInfo::STAFF_TYPE_OS_ORDER_LONG]])
            ->one();

        $before      = [];
        // 如果不存在或者是外包外协
        if ($order['hire_os_type'] == BaseStaffInfo::HIRE_OS_TYPE_CROWD_SOURCING) {
            $staff_model = Staff::combination($staff_arr);//格式化
        } else {
            if (!empty($base_staff_info)) {
                $staff_arr['staff_info_id'] = $base_staff_info->staff_info_id;
                $before                     = Staff::view($base_staff_info->staff_info_id) ?? [];
            }
            $staff_model = Staff::combination($staff_arr);//格式化
        }
        $currentOperateId = Yii::$app instanceof yii\console\Application ? '-1' : $fbid;
        $res = Staff::save($staff_model, $currentOperateId, true);
        Yii::$app->logger->write_log([
            'result'    => $res,
            'serial_no' => $serial_no,
            'staff_arr' => $staff_arr,
        ], 'info');
        //更新员工工号
        $o_detail  = HrOutsourcingOrderDetail::find()
            ->where(['identity' => $detail_value['identity']])
            ->andWhere(['is_del' => HrOutsourcingOrderDetail::IS_DEL_NO])
            ->andWhere(['serial_no' => $serial_no])
            ->one();
        if ($res === true) {
            $after = Staff::view($staff_model->staff_info_id);
            $this->setStaffOperateLogs($staff_model->staff_info_id, $before, $after,$fbid);

            $o_detail->staff_info_id           = $staff_model->staff_info_id;
            $o_detail->is_effective            = 1;//变更成已生效
            $o_detail->effective_state_message = (string)$staff_model->staff_info_id;
            $os_detail_result = $o_detail->save();//更新员工工号
            if ($os_detail_result !== true) {
                Yii::$app->logger->write_log([
                    'staff_info_id' => $staff_model->staff_info_id,
                    'serial_no' => $serial_no,
                    'result' => json_encode($o_detail->getErrors(), JSON_UNESCAPED_UNICODE),
                    'message' => '更新HrOutsourcingOrderDetail失败',
                ]);
            }
            //更新员工班次
            $this->setStaffShift($staff_model->staff_info_id, $order['shift_id'], $order['effective_date']);
        }else{
            $o_detail->effective_state_message = json_encode($res, JSON_UNESCAPED_UNICODE);
            $os_detail_result = $o_detail->save();//更新员工工号
        }

        return $res;
    }

    /**
     * 更新外协员工班次
     * @param $staff_info_id
     * @param $shift_id
     * @param $effective_date
     * @return bool
     */
    public function setStaffShift($staff_info_id, $shift_id, $effective_date): bool
    {
        $staff_shift_model = HrStaffShift::findOne($staff_info_id);
        $shift = Yii::$app->sysconfig->getShift($shift_id);
        if (!$staff_shift_model) {
            $staff_shift_model = new HrStaffShift();
            $staff_shift_model->staff_info_id = $staff_info_id;
        }
        $staff_shift_model->start           = $shift['start'];
        $staff_shift_model->end             = $shift['end'];
        $staff_shift_model->shift_type      = $shift['type'];
        $staff_shift_model->shift_id        = $shift_id;
        $staff_shift_model->effective_date  = $effective_date;//生效时间
        $staff_shift_model->last_start      = $shift['start'];//上个班次开始时间
        $staff_shift_model->last_end        = $shift['end'];  //上个班次结束时间
        $staff_shift_model->last_shift_type = $shift['type']; //上个班次类型
        $staff_shift_model->last_shift_id   = $shift_id;      //上个班次id
        return $staff_shift_model->save();
    }

    /**
     * @param $staff_info_id
     * @param $before
     * @param $after
     * @param $operater_id
     * @return bool
     */
    public function setStaffOperateLogs($staff_info_id, $before, $after,  $operater_id = 0): bool
    {
        $log                = new HrOperateLogs();
        $log->operater      = $operater_id ?: '-1';
        //$log->request_body  = json_encode([]);
        $log->before        = json_encode(['body' => $before ?? []]);
        $log->after         = json_encode(['body' => $after]);
        $log->type          = 'staff';
        $log->staff_info_id = $staff_info_id;
        return $log->save();
    }

    /**
     * 迁移出 sysconfig outsourcing_search_job_title字段
     * @return mixed
     */
    public function searchJobTitles()
    {
        return Yii::$app->cache->getOrSet('conf.out_sourcing.courier.job_title_v1', function ($cache) {
            $out_sourcing_courier = SettingEnv::find()->where([
                'code' => [
                    'out_sourcing_courier',
                    'out_sourcing_store_keeper',
                    'out_sourcing_hub',
                ],
            ])->indexBy('code')->asArray()->all();
            $job_title_ids        = [];

            if (isset($out_sourcing_courier['out_sourcing_courier'])) {
                $job_title_ids = array_filter(array_unique(array_merge($job_title_ids,
                    (array)explode(",", $out_sourcing_courier['out_sourcing_courier']['set_val']))));
                $job_title_ids = array_values($job_title_ids);
            }
            if (isset($out_sourcing_courier['out_sourcing_store_keeper'])) {
                $job_title_ids = array_filter(array_unique(array_merge($job_title_ids,
                    (array)explode(",", $out_sourcing_courier['out_sourcing_store_keeper']['set_val']))));
                $job_title_ids = array_values($job_title_ids);
            }

            if (isset($out_sourcing_courier['out_sourcing_hub'])) {
                $job_title_ids = array_filter(array_unique(array_merge($job_title_ids,
                    (array)explode(",", $out_sourcing_courier['out_sourcing_hub']['set_val']))));
                $job_title_ids = array_values($job_title_ids);
            }

            return $job_title_ids;
        }, 5 * 60);
    }

    /**
     * @description:外协员工-职位和车辆类型对应关系
     * @param null
     * @return:
     * @author: L.J
     * @time: 2022/12/5 15:05
     */
    public function getJobTitleCarList()
    {
        $out_job_title_car_list = SettingEnvService::getInstance()->getSetVal('out_job_title_car_list');
        //{'13':'Bike','110':'Van','452':'Boat','1000':'Tricycle'}
        return empty($out_job_title_car_list) ? [] : json_decode($out_job_title_car_list, true);
    }

    public function importOrder($excel_data, $permission_role, $fbid): array
    {
        return [];
    }

    public function getImportOrderTpl(): array
    {
        //v1 版本 增加个人邮箱
        return ['url' => Yii::$app->lang->get('out_sourcing_order_tpl_common_v1')];
    }


    protected function getSmsContent($param): string
    {
        $shift           = Yii::$app->sysconfig->getShift($param['shift_id']);
        $employment_date = $param['employment_date'];//雇佣日期
        $staff_shift     = $shift['start'] . '-' . $shift['end'];
        $job_title       = $param['job_title'];
        $sys_store_name  = '';
        if (!empty($param['sys_store_id'])) {
            $sys_store_name = $param['sys_store_id'] == '-1' ? Yii::$app->lang->get('head_office') : Staff::getStoreInfo($param['sys_store_id'])['name'];
        }

        $baseUrl  = env("h5_url", "https://msg.flashexpress.com/");
        $bindData = [
            'serial_no'   => $param['serial_no'],
            'identity'    => $param['identity'],
            'job_title'   => $job_title,
            'staff_shift' => $staff_shift,
            'tel'         => '0646520728,0646520525',
        ];
        $long_url = '';
        //快递员
        if (in_array($job_title, Yii::$app->sysconfig->out_sourcing_courier_job_title)) {
            $long_url = $baseUrl . "th/outsource/courier?";
        }
        //仓管
        if (in_array($job_title, Yii::$app->sysconfig->out_sourcing_store_keeper_job_title)) {
            $long_url = $baseUrl . "th/outsource/keeper?";
        }
        if (in_array($job_title, Yii::$app->sysconfig->out_sourcing_store_keeper_job_title)) {
            $bindData['tel'] = "************";
            if ($job_title == 271) {
                $bindData['tel'] = "************";
            }
        }
        $short_url = $long_url ? OtherService::getShortUrl($long_url . urldecode(http_build_query($bindData))) : '';
        return "สวัสดี กรุณาเข้างาน $employment_date - $staff_shift ที่ $sys_store_name กรุณายืนยันข้อมูลส่วนตัว ชื่อ-สกุล " . $short_url;
    }


    //发送短信
    public function send_sms_staff_info($mobile, $param)
    {
        Yii::$app->jrpc->smsSend($mobile, $this->getSmsContent($param), 'out_sourcing');
    }

    /**
     * 权限 查看外协员工工作订单按照管辖范围读取数据,配置的角色 按照管辖范围查看
     * 没有配置的网点员工智能查看所属网点的数据
     * 不在配置项的总部员工查看所有订单数据
     * @param $query
     * @param $user
     * @return mixed
     */
    public function outSourcingOrderListQuery($query, $user) {
        $staff_info_id = $user['staff_id'];
        $setting_val = SettingEnvService::getInstance()->getSetVal('outsourcing_order_check_role_id');
        $check_role_ids = explode(',', $setting_val);
        $staff_roles = HrStaffInfoPosition::find()->select('position_category')->where(['staff_info_id' => $staff_info_id])->column();
        $user_store_id = $user['staff_store_id'];
        if(array_intersect($check_role_ids, $staff_roles)) {
            //管辖部门/管辖大区/管辖片区/管辖网点/管辖网点类型
            $jurisdiction_result = Staff::dominDepartmentsAndStores($staff_info_id);
            $and_conditions = [];
            if(isset($jurisdiction_result['flash_home_departments']) && !empty($jurisdiction_result['flash_home_departments'])) {
                $and_conditions['department_id'] = $jurisdiction_result['flash_home_departments'];
            }
            $and_conditions['store_id'] = $jurisdiction_result['stores'] ?? [];
            array_push($and_conditions['store_id'], $user_store_id);
            $or_conditions[] = 'OR';
            foreach ($and_conditions as $k => $v) {
                if ($k == 'store_id' && in_array('-2', (array) $v)) {
                    $or_conditions[] = ['NOT IN', 'hr_outsourcing_order.' . $k, ['-1']];
                } else {
                    $or_conditions[] = ['IN', 'hr_outsourcing_order.' . $k, $v ?? []];
                }
            }
            $query->andWhere($or_conditions);
        } else {
            if($user_store_id != '-1') {
                //网点员工只能看所属网点数据，总部员工能看所有工单数据
                $query->andWhere(['hr_outsourcing_order.store_id' => $user_store_id]);
            }
        }
        return $query;
    }

    /**
     * 获取by申请信息
     * @param $params
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
    public function getOutsourcingApplyList($params): array
    {
        $serial_nos = $params['serial_nos'] ?? [];

        if(empty($serial_nos)) {
            return [];
        }

        return HrStaffOutsourcing::find()->select([
            'id',
            'serial_no',
            'staff_id',
            'os_type',
            'job_id',
            'department_id',
            'store_id',
            'employment_date',
            'employment_days',
            'shift_id',
            'demend_num',
            'final_audit_num',
            'reason_type',
            'reason',
            'status',
            'need_remark',
        ])
            ->where(['serial_no' => $serial_nos])
            ->asArray()
            ->indexBy('serial_no')
            ->all(Yii::$app->get('r_backyard'));
    }

    /**
     * 外协申请原因类型
     * @param string $language
     * @return array
     */
    public function getOutsourcingApplyReasonType(string $language = 'th'): array
    {
        return [
            0 => Yii::$app->lang->get('os_request_reason_other', '', $language),
            1 => Yii::$app->lang->get('os_request_reason_faraway', '', $language),
            2 => Yii::$app->lang->get('os_request_reason_resign', '', $language),
            3 => Yii::$app->lang->get('os_request_reason_nocar', '', $language),
            4 => Yii::$app->lang->get('os_request_reason_stopout', '', $language),
            5 => Yii::$app->lang->get('os_request_reason_highavg', '', $language),
            6 => Yii::$app->lang->get('os_request_reason_daily', '', $language),
            7 => Yii::$app->lang->get('os_request_reason_big_promotion', '', $language),
            8 => Yii::$app->lang->get('os_request_reason_special_customer', '', $language),

        ];
    }

    /**
     * 外协可选班次
     * @param $job_title_id
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
    public function getOutsourcingShifts($job_title_id = 0): array
    {
        $result     = [];
        $shift_list = HrShift::find()->where(['shift_group' => HrShift::SHIFT_GROUP_FULL_DAY_SHIFT])->orderBy('start')->all(Yii::$app->get('r_backyard'));
        foreach ($shift_list as $obj) {
            $result[$obj->type][] = [
                'start'  => $obj->start,
                'end'    => $obj->end,
                'id'     => $obj->id,
                'type'   => $obj->type,
                'markup' => '(' . Yii::$app->lang->get('shift_' . $obj->type) . ') ' . $obj->start . '-' . $obj->end,
            ];
        }
        ksort($result);
        return $result;
    }

    /**
     * 上传excel 批量获取在执行的众包任务
     * @param $data
     * @param $order_list
     * @return bool|array
     */
    public function getCrowdSourcingTasks($value, $orderData): array
    {
        $zb_task = [];
        $params  = [];

        if(empty($value[1]) && empty($value[4]) && empty($value[8])){
            return false;
        }
        if(empty($orderData)){
            return false;
        }
        $effective_date  = $orderData['effective_date'] ?? '';
        $invalid_date    = $orderData['invalid_date'] ?? '';
        $start_timestamp = !empty($effective_date) ? strtotime($effective_date) : '';
        $end_timestamp   = !empty($invalid_date) ? strtotime($invalid_date) : '';
        $params[]        = [
            'card_num'        => trim($value[1]),
            'mobile'          => trim($value[4]),
            'driver_card_num' => trim($value[8]),
            'start_time'      => $start_timestamp,
            'end_time'        => $end_timestamp,
        ];
        if (!empty($params)) {
            $zb_task = OutsourcingOrder::getCrowdSourcingCheckList($params);
        }
        return $zb_task;
    }

    public function addStaffShiftToMiddle($staff_info_id, $employment_date, $employment_days, $shift)
    {
        $employment_days = $employment_days - 1;
        $dateList        = DateHelper::DateRange(strtotime($employment_date),
            strtotime($employment_date . ' +' . $employment_days . ' days'));

        $today       = gmdate('Y-m-d', time() + TIME_ADD_HOUR * 3600);
        $update_date = max($today, $employment_date);

        HrStaffShiftMiddleDate::updateAll(
            ['deleted' => 1], "staff_info_id = $staff_info_id and shift_date >= '" . $update_date . "'"
        );
        foreach ($dateList as $date) {
            if ($date < $today) {
                continue;
            }
            $insertData[] = [
                $staff_info_id,
                $shift['id'],
                $shift['start'],
                $shift['end'],
                $shift['type'],
                $date,
            ];
        }
        !empty($insertData) && Yii::$app->backyard_main->createCommand()->batchInsert(
            HrStaffShiftMiddleDate::tableName(),
            ['staff_info_id', 'shift_id', 'shift_start', 'shift_end', 'shift_type', 'shift_date'],
            $insertData
        )->execute();
    }


    /**
     * 外协员工工作订单列表
     * @param $params
     * @param $permission
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
    public function workOrderList($params): array
    {
        //审批编号
        if (!empty($params['serial_no'])) {
            $where_serial_no = $params['serial_no'];
        }

        //工单id
        if (!empty($params['serial_id'])) {
            $where_serial_id = $params['serial_id'];
        }

        //部门
        $is_query_department = true;
        if (!empty($params['department'])) {
            $where_department = $params['department'];
            // 根据 传参 is_sub_department 取决于是否查询子部门
            $is_query_department = SysDepartmentService::getSubDepartmentByIsSub($where_department, [
                'is_sub_department' => $params['is_sub_department'],
            ]);
        }

        //网点
        if (!empty($params['store_id'])) {
            $where_store = $params['store_id'];
        }

        //职位
        if (!empty($params['job_title'])) {
            $where_job_title = $params['job_title'];
        }

        //状态 1待生效2已生效3.已结束4已取消
        if (!empty($params['status'])) {
            $where_status = $params['status'];
        }

        //班次

        if (!empty($params['shift'])) {
            $where_shift = $params['shift'];
        }

        //外协工单类型 1 短期外协 2 长期外协

        if (!empty($params['os_type'])) {
            $where_os_type = $params['os_type'];
        }

        //外协雇佣类型

        if (!empty($params['hire_os_type'])) {
            $hire_os_type = $params['hire_os_type'];
        }

        //hub外协公司id
        if (!empty($params['out_company_id'])) {
            $out_company_id = $params['out_company_id'];
        }

        $query = HrOutsourcingOrder::find();

        $query->andFilterWhere(['hr_outsourcing_order.id' => $where_serial_id ?? null])
//                ->andFilterWhere(['hr_outsourcing_order.serial_no' => $where_serial_no ?? null])
            ->andFilterWhere(['hr_outsourcing_order.job_id' => $where_job_title ?? null])
            //->andFilterWhere(['hr_outsourcing_order.department_id' => $where_department ?? null])
            ->andFilterWhere(['hr_outsourcing_order.store_id' => $where_store ?? null])
            ->andFilterWhere(['hr_outsourcing_order.shift_id' => $where_shift ?? null])
            ->andFilterWhere(['hr_outsourcing_order.status' => $where_status ?? null])
            ->andFilterWhere(['hr_outsourcing_order.os_type' => $where_os_type ?? null])
            ->andFilterWhere(['hr_outsourcing_order.hire_os_type' => $hire_os_type ?? null]);

        if (!empty($where_serial_no)) {
            $query->andFilterWhere(['LIKE', 'hr_outsourcing_order.serial_no', $where_serial_no]);
        }
        if ($is_query_department && !empty($where_department)) {
            $query->andFilterWhere(['hr_outsourcing_order.department_id' => $where_department]);
        }

        //查询外协公司
        if (!empty($out_company_id)) {
            $query->andFilterWhere(['hr_outsourcing_order.out_company_id' => $out_company_id]);
        }

        //网点
        $query->leftJoin('sys_store', 'hr_outsourcing_order.store_id = sys_store.id');
        $query->select(["hr_outsourcing_order.*", "sys_store.manage_piece"]);
        if (!empty($params["region"]) || !empty($params['piece'])) {
            $region      = $params["region"] ?? null;
            $piece       = $params["piece"] ?? null;
            $store_query = SysStore::find();
            //查找网点  泰国一个大区网点最多 150 个 可以 in
            if ($region) {
                $store_query->andFilterWhere(['manage_region' => $region]);
            }
            //找片区的网点 一个片区最多 150 网点
            if ($piece) {
                $store_query->andFilterWhere(['manage_piece' => $piece]);
            }
            $store_list = $store_query->asArray()->all();
            $store_list = !empty($store_list) ? array_column($store_list, 'id') : null;
            $query->andFilterWhere(['IN', 'hr_outsourcing_order.store_id', $store_list]);
        }

        //雇佣日期
        if (!empty($params['employment_date_bengin']) && !empty($params['employment_date_end'])) {
            $where_date_begin = $params['employment_date_bengin'];
            $where_date_end    = $params['employment_date_end'];
            $query->andFilterWhere([
                'between',
                'hr_outsourcing_order.employment_date',
                $where_date_begin,
                $where_date_end,
            ]);
        }
        //权限 查看外协员工工作订单按照管辖范围读取数据,配置的角色 按照管辖范围查看
        //没有配置的网点员工智能查看所属网点的数据
        //不在配置项的总部员工查看所有订单数据
        $query = $this->outSourcingOrderListQuery($query, ['staff_id' => $params['operate_id'],'staff_store_id' => $params['operate_store_id']]);
        $data['page_count'] = $query->count('*', Yii::$app->get('r_backyard'));

        $data['rows'] = $query->orderBy(['hr_outsourcing_order.id' => SORT_DESC])
            ->offset($params['page'] * $params['page_num'])
//                ->indexBy('serial_no')
            ->limit($params['page_num'])
            ->asArray()
            ->all(Yii::$app->get('r_backyard'));
        //获取已配置人数,片区,班次信息
        $packaging_items   = OutsourcingOrder::getPackagingItems($data['rows']);
        $config_count_list = $packaging_items['config_count_list'] ?? []; //已配置人数
        $piece             = $packaging_items['piece'] ?? [];             //获取片区
        $shift_list        = $packaging_items['shift_list'] ?? [];        //获取班次信息

        $companyInfo     = OutSourcingService::getInstance()->getOsCompanyAll(['id', 'company_name']);
        $companyInfoToId = array_column($companyInfo, 'company_name', 'id');

        $store_ids = array_column($data['rows'], 'store_id');
        $storeTemp = SysStoreService::getInstance()->getDataUseIdIndex(['id', 'name'], null, ['id' => $store_ids]);
        //长期外协 可编辑的权限 去掉按角色改成按工号
        $longEditPermission = SettingEnvService::getInstance()->getSetVal('outsourcing_edit_long', ',');
        $outSourcingOrderService = OutSourcingOrderService::getInstance();
        foreach ($data['rows'] as $key => $one) {
            //生效时间计算
            $current_time = gmdate('Y-m-d H:i:00', time() + TIME_ADD_HOUR * 3600);//当前时间，泰国时间
            //最后一天班次开始前一小时 当前日期+(雇佣天数-1)
            $last_effective_time = gmdate('Y-m-d H:i:s',
                strtotime($one['employment_date'] . ($shift_list[$one['shift_id']]['start'] ?? '')) + 3600 * 24 * ($one['employment_days'] - 1) - 3600);

            $shift_type  = '';
            $shift_begin = $one['shift_begin_time'];
            $shift_end   = $one['shift_end_time'];
            if (isset($shift_list[$one['shift_id']])) {
                $shift_type  = $shift_list[$one['shift_id']]['type'];
                $shift_begin = $shift_list[$one['shift_id']]['start'];
                $shift_end   = $shift_list[$one['shift_id']]['end'];
            }

            $storeName                                 = isset($storeTemp[$one['store_id']]) ? $storeTemp[$one['store_id']]['name'] : $one['store_id'];
            $data['rows'][$key]['sys_store_name']      = $storeName;
            $data['rows'][$key]['job_title_name']      = Yii::$app->sysconfig->jobTitle[$one['job_id']] ?? '';              //职位名称
            $data['rows'][$key]['sys_department_name'] = Yii::$app->sysconfig->allDepeartments[$one['department_id']] ?? '';//部门名称
            $data['rows'][$key]['status_name']         = Yii::$app->lang->get(strtolower('configure_' . $one['status']),'',$params['lang']);
            $data['rows'][$key]['shift_type']          = $shift_type;//班次类型
            $data['rows'][$key]['shift_start']         = $shift_begin;
            $data['rows'][$key]['shift_end']           = $shift_end;
            $data['rows'][$key]['name']                = $piece[$one['manage_piece']] ?? ''; //片区
            $data['rows'][$key]['created_at']          = date('Y-m-d H:i:s',
                strtotime($data['rows'][$key]['created_at']) + TIME_ADD_HOUR * 3600);
            $data['rows'][$key]['approve_create_time'] = DateHelper::show_time_zone($data['rows'][$key]['approve_create_time']);
            $data['rows'][$key]['config_count']        = $config_count_list[$one['serial_no']] ?? 0;//已配置人数
            //待生效的功能可以编辑
            $data['rows'][$key]['is_edit']           = 0;
            $data['rows'][$key]['hire_os_type_text'] = Yii::$app->lang->get(strtolower('hire_type_' . $one['hire_os_type']),'',$params['lang']);

            if ($one['os_type'] == 1) {
                if (in_array(YII_COUNTRY, ['PH', 'MY'])) {
                    if (in_array($data['rows'][$key]['status'], [1, 2])) {
                        $validate = OutsourcingOrder::validate_edit_order($params['fbi.role'], $one['job_id']);
                        if ($validate) {
                            $data['rows'][$key]['is_edit'] = 1;
                        }
                    }
                } else {
                    if (in_array($data['rows'][$key]['status'], [1, 2]) && $current_time < $last_effective_time) {
                        $validate = OutsourcingOrder::validate_edit_order($params['fbi.role'], $one['job_id']);
                        if ($validate) {
                            $data['rows'][$key]['is_edit'] = 1;
                        }
                    }
                }
            } else {
                if (in_array($params['operate_id'], $longEditPermission)) {
                    $data['rows'][$key]['is_edit'] = in_array($data['rows'][$key]['status'], [1, 2]) ? 1 : 0;
                }
//                if (in_array('HRIS_MANAGER', $params['fbi.role'])) { //长期外协 操作权限 hris 管理员
//                    $data['rows'][$key]['is_edit'] = $data['rows'][$key]['status'] == 1 ? 1 : 0;
//                }
            }
            //如果是 ffm 申请的订单 带生效和已生效 都能编辑
            if (in_array($one['status'], [1, 2]) && $one['source_category'] == 1) {
                $data['rows'][$key]['is_edit'] = 1;
            }
            $data['rows'][$key]['out_company_name'] = $companyInfoToId[$one['out_company_id']] ?? '';
        }


        $data['rows'] = array_values($data['rows'] ?? []);
        return $data;
    }


    /**
     * 导出配置明细-导出任务
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function addDownloadDetailExcelTask($params): bool
    {
        $action_name = 'outsourcing-staff/download-detail';
        $hcmExcelTaskExists = HcmExcelTask::find()->where(['staff_info_id' => $params['operate_id']])->andWhere(['action_name' => $action_name])->andWhere(['status' => 0])->andWhere(['is_delete' => 0])->exists();
        if($hcmExcelTaskExists) {
            throw new BusinessException(Yii::$app->lang->get('download_001'));
        }
        $file_name = Yii::$app->params['csv_file_name_prefix']. 'outsource_order_detail'.time().'.xlsx';
        //hcm用导出列表
        $hcmExcelTask = new HcmExcelTask();
        $hcmExcelTask->staff_info_id = (string)$params['operate_id'];
        $hcmExcelTask->file_name  = $file_name;
        $hcmExcelTask->executable_path = Yii::$app->basePath.'/yii';
        $hcmExcelTask->action_name = $action_name;
        $hcmExcelTask->created_at = gmdate('Y-m-d H:i:s', time() + TIME_ADD_HOUR*3600);
        $hcmExcelTask->args_json = json_encode($params,JSON_UNESCAPED_UNICODE);
        return $hcmExcelTask->save();
    }

    /**
     * 导出配置明细-具体数据
     * @param $params
     * @return array[]
     * @throws InvalidConfigException
     */
    public function downloadDetail($params): array
    {
        $returnData         = [];
        $params['page']     = 0;
        $params['page_num'] = 200;
        $lang               = $params['lang'] ?? 'en';
        $appLang            = Yii::$app->lang;
        $appLang->setLang($lang);
        while (true) {
            $currentData = $this->workOrderList($params);
            if (empty($currentData['rows'])) {
                break;
            }
            $listData = $currentData['rows'];
            foreach ($listData as $one) {
                $findDetails = OutsourcingOrder::view($one['serial_no'], 1);
                $details     = $findDetails['detail'] ?? [];
                if (empty($details)) {
                    continue;
                }
                $operateStaffIds = array_column($details, 'operate_id');
                $staffIds        = array_values(array_unique(array_filter($operateStaffIds)));
                $staffInfo       = $this->getStaffInfos($staffIds);
                $base            = [
                    $one['id'],
                    $one['serial_no'],
                    $one['sys_department_name'],
                    $one['sys_store_name'],
                    $one['name'],//片区
                    $one['hire_os_type_text'],
                    $one['job_title_name'],
                    $one['employment_date'],
                    $one['employment_days'],
                    $one['shift_begin_time'] . '-' . $one['shift_end_time'],
                    $one['status_name'],
                    $one['approve_create_time'] ?? '',//审批申请时间
                    $one['created_at'],               //订单创建时间
                ];
                foreach ($details as $detail) {
                    $detailBase = [
                        empty($detail['staff_info_id']) ? '' : $detail['staff_info_id'],
                        $detail['identity'],
                        $detail['staff_name'],
                        $appLang->get('sex_' . $detail['sex'], ''),
                        $detail['mobile'],
                        $detail['personal_email'],
                        $appLang->get('outsourcing_type_' . $detail['outsourcing_type'], ''),
                        $appLang->get('pay_type_' . strtolower($detail['pay_type']), ''),
                        $detail['company_name_ef'],
                        $detail['driver_license'],
                        $detail['car_no'],
                        $detail['bank_id_text'],
                        $detail['bank_no_name'],
                        $detail['bank_no'],
                    ];
                    if (YII_COUNTRY == 'MY') {
                        $detailBase[] = $detail['residential_address'];
                        $detailBase[] = $detail['relatives_call_name'];
                        $detailBase[] = empty($detail['relatives_relationship']) ? '' : $appLang->get('relatives_relationship_' . $detail['relatives_relationship'],
                            '');
                        $detailBase[] = $detail['relatives_mobile'];
                    }
                    $detailBase[] = $detail['effective_state_message'];
                    $detailBase[] = $detail['configured_staff'] ?? '';
                    $detailBase[] = $detail['create_at'];                                                        //配置时间
                    $detailBase[] = $this->getConfigState($appLang,
                        $detail);                                                                                //配置状态
                    $isDel        = $detail['is_del'] == 1 && $detail['operate_id'];                             //第二个条件兼容历史数据
                    $detailBase[] = $isDel ? "({$detail['operate_id']}){$staffInfo[$detail['operate_id']]}" : '';//删除/更换人
                    $detailBase[] = $isDel ? $detail['updated_at'] : '';                                         //删除/更换时间
                    $returnData[] = array_merge($base, $detailBase);
                }
            }
            ++$params['page'];
        }
        return ['header' => $this->getDownloadDetailHeader($appLang), 'data' => $returnData];
    }

    /**
     * 获取订单详情配置状态 已配置、已删除、被更换
     * @param $detail
     * @param $lang
     * @return string
     */
    protected function getConfigState($appLang, $detail): string
    {
        //已配置
        if ($detail['is_del'] == 0) {
            return $appLang->get('out_config_state_configured', '');
        }
        //已删除
        if ($detail['is_del'] == 1 && $detail['be_replaced'] == 0) {
            return $appLang->get('out_config_state_deleted', '');
        }
        //被更换
        if ($detail['is_del'] == 1 && $detail['be_replaced'] == 1) {
            return $appLang->get('out_config_state_changed', '');
        }
        return '';
    }
    /**
     * 获取员工信息
     * @param $staffIds
     * @return array
     */
    public function getStaffInfos($staffIds): array
    {
        $staff_info = BaseStaffInfo::find()
            ->select(['staff_info_id', 'name'])
            ->where(['in','staff_info_id' , $staffIds])
            ->asArray()
            ->all();
        return array_column($staff_info, 'name','staff_info_id');
    }

    /**
     * 导出配置明细-具体数据-表头信息
     * @param $lang
     * @return array
     */
    protected function getDownloadDetailHeader($appLang): array
    {
        $base    = [
            $appLang->get('out_work_order_id', ''),            //工作订单ID
            $appLang->get('out_approval_number', ''),          //审批编号
            $appLang->get('out_department', ''),               //部门
            $appLang->get('out_work_branch', ''),              //所属网点
            $appLang->get('out_district', ''),                 //片区
            $appLang->get('os_outsourcing_hire_type', ''),     //外协雇佣类型
            $appLang->get('out_position', ''),                 //外协职位
            $appLang->get('out_date_employment', ''),          //雇佣日期
            $appLang->get('out_employment_days', ''),          //雇佣天数
            $appLang->get('out_shift', ''),                    //工作班次
            $appLang->get('out_work_order_state', ''),         //订单状态
            $appLang->get('out_order_approval_start_time', ''),//审批申请时间
            $appLang->get('out_order_create_pass_time', ''),   //订单创建时间(审批通过时间)


            $appLang->get('out_history_id', ''),           //历史外协工号
            $appLang->get('excel_identity', ''),           //身份证号
            $appLang->get('excel_staff_name', ''),         //姓名
            $appLang->get('excel_gender', ''),             //性别
            $appLang->get('excel_mobile_number', ''),      //手机号
            $appLang->get('excel_personal_email', ''),     //个人邮箱
            $appLang->get('outsourcing_type', ''),         //外协员工类型
            $appLang->get('excel_pay_type', ''),           //结算类型
            $appLang->get('excel_outsourcing_company', ''),//外协公司
            $appLang->get('excel_driver_license', ''),     //驾驶证号
            $appLang->get('excel_license_no', ''),         //车牌号
            $appLang->get('excel_bank', ''),               //银行
            $appLang->get('excel_cardholder', ''),         //银行卡持卡人
            $appLang->get('out_card_no', ''),              //银行账号
        ];

        $baseSecond = [
            $appLang->get('out_effect_state', ''),             //生效状态
            $appLang->get('out_config_people', ''),            //配置人
            $appLang->get('out_config_time', ''),              //配置时间
            $appLang->get('out_config_state', ''),             //配置状态
            $appLang->get('out_detail_delete_change_peo', ''), //删除/更换人
            $appLang->get('out_detail_delete_change_time', ''),//删除/更换时间
        ];
        if (YII_COUNTRY == 'MY') {
            $MY = [
                $appLang->get('residential_address', ''),        //居住地址
                $appLang->get('emergency_contact', ''),          //紧急联系人
                $appLang->get('out_emergency_contact_re', ''),   //紧急联系人关系
                $appLang->get('out_emergency_contact_phone', ''),//紧急联系人电话号码
            ];
            return array_merge($base, $MY, $baseSecond);
        }
        return array_merge($base, $baseSecond);
    }

    /**
     * 获取1待配置2已配置 订单 个人邮箱
     * @param array $personalEmails
     * @return array
     */
    public function getConfigOrConfiguredOrder(array $personalEmails = []): array
    {
        $returnData = [];
        $find       = HrOutsourcingOrder::find()->alias('hoo')->innerJoin('hr_outsourcing_order_detail hood',
            'hoo.serial_no = hood.serial_no')
            ->select(['hoo.serial_no', 'hoo.effective_date', 'hoo.invalid_date', 'hood.personal_email'])
            ->where(['in', 'hoo.status', [1, 2]]);
        if ($personalEmails) {
            $personalEmails = array_map(function ($email) {
                return trim($email);
            }, $personalEmails);
            $find->andWhere(['in', 'hood.personal_email', $personalEmails]);
        }
        $list = $find->andWhere("hood.is_del=0 and hood.personal_email!=''")
            ->asArray()->all();
        foreach ($list as $value) {
            $returnData[$value['personal_email']][] = [
                'start'     => $value['effective_date'],
                'end'       => $value['invalid_date'],
                'serial_no' => $value['serial_no'],
            ];
        }
        return $returnData;
    }

    /**
     * 获取1待配置2已配置 订单 身份证号
     * @param array $identities
     * @param array $excludeSerialNos
     * @return array
     */
    public function getConfigOrConfiguredOrderForIdentity(array $identities, array $excludeSerialNos = []): array
    {
        $returnData = [];
        if (empty($identities)) {
            return $returnData;
        }
        $identities = array_map(function ($id) {
            return trim($id);
        }, $identities);
        $find = HrOutsourcingOrder::find()->alias('hoo')->innerJoin('hr_outsourcing_order_detail hood',
            'hoo.serial_no = hood.serial_no')
            ->select(['hoo.serial_no', 'hoo.effective_date', 'hoo.invalid_date', 'hood.identity'])
            ->where(['in', 'hoo.status', [1, 2]])
            ->andWhere(['in', 'hood.identity', $identities]);
        if ($excludeSerialNos) {
            $find->andWhere(['not in', 'hoo.serial_no', $excludeSerialNos]);
        }
        $list = $find->andWhere("hood.is_del=0 and hood.identity!=''")
            ->asArray()->all();
        foreach ($list as $value) {
            $returnData[$value['identity']][] = [
                'start'     => $value['effective_date'],
                'end'       => $value['invalid_date'],
                'serial_no' => $value['serial_no'],
            ];
        }
        return $returnData;
    }

    /**
     * 同一个「个人邮箱/身份证」所在不同订单的生效期间重叠订单
     * @param $existData
     * @param $startTime
     * @param $endTime
     * @return array
     */
    public function checkEffectiveTimeDuplicated($existData, $startTime, $endTime): array
    {
        $personalEmailSerialNos = [];
        foreach ($existData as $item) {
            if (($startTime <= $item['end']) && ($item['start'] <= $endTime)) {
                $personalEmailSerialNos[] = $item['serial_no'];
            }
        }
        return array_values(array_unique($personalEmailSerialNos));
    }
}