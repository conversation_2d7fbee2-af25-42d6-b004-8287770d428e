<?php

namespace app\services\base\validations;

use app\services\base\BaseService;
use WebGeeker\Validation\Validation;
use WebGeeker\Validation\ValidationException;
use yii\base\ErrorException;
use Yii;

abstract class BaseValidation extends BaseService {

    const SCENES_CONSTANT_PREFIX = "SCENES_VALIDATION_";


    abstract protected function rules();


    /**
     * @param $params
     * @param $scene
     * @return mixed
     * @throws ErrorException
     * @throws ValidationException
     *
     */
    public function validation($params, $scene)
    {

        $this->checkScenesConstant($scene);

        $rules = $this->rules();

        $validations = [];
        $functions = [];

        foreach ($rules as $rule) {
            $field = $rule[0];
            $validate = $rule[1];
            $errMsg = $rule[2];
            $scenes = $rule[3];
            if (in_array($scene, $scenes)
                &&  isset($params[$field]) && $params[$field] !== ""
            ) {
                if ($field == 'FUNCTIONS') {
                    $functions[] = $rule;
                    continue;
                }
                // field 会覆盖前面的验证!!
                if ($errMsg) {
                    $validations[$field] = $validate . "|>>>:" . $errMsg;
                } else {
                    $validations[$field] = $validate;
                }
            }
        }

        if ($validations) {

            try {
                Validation::validate($params, $validations);
            } catch (ValidationException $exception) {
                Yii::$app->logger->write_log([
                    'params' => $params,
                    'validationMsg' => $exception->getMessage()
                ], 'info');
                // 记完日志 继续往上抛出去
                throw $exception;
            }
        }

        foreach ($functions as $rule) {
            try {

                $result = call_user_func_array([$this, $rule[1]], [$params]);

                if ($result === false) {
                    throw new ValidationException($rule[2]);
                }

            } catch (ValidationException $exception) {
                Yii::$app->logger->write_log([
                    'params' => $params,
                    'validationMsg' => $exception->getMessage()
                ], 'info');
                // 记完日志 继续往上抛出去
                throw $exception;
            }
        }


        return $params;

    }




    /**
     * @param $scene
     * @return bool
     * @throws ErrorException
     *
     */
    private function checkScenesConstant($scene)
    {

        $reflectClass = new \ReflectionClass($this);

        $constants = $reflectClass->getConstants();

        foreach ($constants as $name => $value) {
            if (strpos($name, self::SCENES_CONSTANT_PREFIX) !== false
                && $value == $scene) {
                return true;
            }
        }

        throw new ErrorException("scenes value not exist");
    }
}