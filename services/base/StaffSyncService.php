<?php

namespace app\services\base;

use app\libs\BusinessException;
use app\libs\Enums\JobTitleEnums;
use app\libs\RocketMQ;
use app\models\backyard\CompanyMobileManage;
use app\models\fle\StaffInfo;
use app\models\fle\StaffInfoPosition;
use app\models\manage\StaffItems;
use Yii;


class StaffSyncService extends BaseService
{

    /**
     * 同步员工信息 to java
     *
     * @param $model
     * @param $isUpdate
     * @return bool
     * @throws BusinessException
     */
    public function saveStaffInfo($model, $isUpdate)
    {
        if (empty($model['sys_store_id'])) {
            Yii::$app->logger->write_log('Mns sys_store_id 能为空', 'info');
            return false;
        }

        if ($model['sys_store_id'] == -1) {
            if ((int)$model['sys_department_id'] == 0) {
                Yii::$app->logger->write_log('Mns sys_department_id 异常;' . json_encode($model), 'info');
                return false;
            }
            $organizationId   = $model['sys_department_id'];
            $organizationType = 2;
        } else {
            if (empty($model['sys_store_id'])) {
                Yii::$app->logger->write_log('Mns sys_store_id 异常;' . json_encode($model), 'info');
                return false;
            }
            $organizationId   = $model['sys_store_id'];
            $organizationType = 1;
        }

        if (empty($model['staff_car_type'])) {
            $vehicle = null;
        } else {
            $vehicle = $model['staff_car_type'] == 'Bike' ? 0 : 1;
        }

        if ($isUpdate && empty($model['staff_info_id'])) {
            Yii::$app->logger->write_log('Mns staff_info_id 异常;' . json_encode($model), 'info');
            return false;
        }

        // 外协 结算类型
        if ($model['formal'] == 0) {
            if (!isset($model['pay_type']) || !in_array($model['pay_type'], [
                    StaffItems::VALUE_PAY_TYPE_BY_DAY,
                    StaffItems::VALUE_PAY_TYPE_BY_MONTH,
                    StaffItems::VALUE_PAY_TYPE_CASH_PAY,
                ])) {
                Yii::$app->logger->write_log('Mns Pay Type Error;' . json_encode($model), 'info');
                return false;
            }
            $payType = strval($model['pay_type']);
        }

        $positions = is_array($model['position_category']) ? $model['position_category'] : explode(',',
            $model['position_category']);
        if (count($positions) == 0) {
            $positions = [98];
        }
        if (!in_array(21, $positions)) {
            $model['area_manager_store'] = [];
        } else {
            $model['area_manager_store'] = isset($model['area_manager_store']) ? array_values($model['area_manager_store']) : [];
        }

        if (in_array($model['formal'], [1, 4])) {
            $formal = 1;
        } else {
            $formal = 0;
        }

        $positions = array_map('intval', $positions);

        $manageArea = [];
        if (!empty($model['manage_area_name'])) {
            $manageArea = array_map('strval', $model['manage_area_name']);
        }

        $validate         = new \yii\validators\DateValidator();
        $validate->format = "yyyy-MM-dd HH:mm:ss";
        $validate->min    = '2000-01-01';

        foreach (['leave_date', 'hire_date', 'stop_duties_date'] as $date) {
            if (!empty($model[$date]) && !$validate->validate(date('Y-m-d H:i:s', strtotime($model[$date])), $error)) {
                Yii::$app->logger->write_log('Mns leave_date/hire_date/stop_duties_date Error;' . json_encode($error),
                    'info');
                return false;
            }
        }

        $department_id = empty($model['sys_department_id']) ? null : (string)$model['sys_department_id'];
        if ($model['node_department_id'] != 0) {
            $department_id = $model['node_department_id'];
        }

        //不在企业号码池的号码 都要清空掉
        $mobile_company = $model['mobile_company'];
        if (isCountry('TH') && !empty($mobile_company) && !empty($model['staff_info_id'])) {
            $isSetPhoneNUmber = CompanyMobileManage::find()->select('phone_number')->where([
                'phone_number'  => $mobile_company,
                'staff_info_id' => $model['staff_info_id'],
                'status'        => CompanyMobileManage::STATUS_ACTIVE,
            ])->one();
            if (empty($isSetPhoneNUmber)) {
                $mobile_company = '';
            }
        }

        $messageBody = [
            'id'                   => intval($model['staff_info_id'] ?? 0),
            'company_name'         => $model['company_name_ef'],
            'name'                 => $model['name'],
            'mobile'               => strval($model['mobile']),
            'mobile_company'       => strval($mobile_company),
            'email'                => $model['email'],
            'personal_email'       => $model['personal_email'] ?? '',
            'positions'            => $positions,
            'vehicle'              => $vehicle,
            'formal'               => $formal,
            'state'                => intval($model['state']),
            'wait_leave_state'     => intval($model['wait_leave_state']),
            'leave_date'           => $model['leave_date'] ? date('Y-m-d', strtotime($model['leave_date'])) : null,
            'operator_id'          => intval($model['operator_id'] ?? -1),
            'event'                => $isUpdate ? 2 : 1,
            'hire_date'            => $model['hire_date'] ? date('Y-m-d', strtotime($model['hire_date'])) : null,
            'organization_id'      => strval($organizationId),
            'organization_type'    => $organizationType,
            'department_id'        => strval($department_id),
            'outsourcing_pay_type' => $payType ?? null,
            'store_id_list'        => [],
            'area_list'            => $manageArea,
            'profile_photo_path'   => isset($model['profile_object_key']) ? strval($model['profile_object_key']) : null,
            'is_sub_staff'         => intval($model['is_sub_staff']),
            'master_staff'         => isset($model['master_staff']) ? intval($model['master_staff']) : null,
            'stop_duties_date'     => $model['stop_duties_date'] ? date('Y-m-d',
                strtotime($model['stop_duties_date'])) : null,
            'job_title'            => intval($model['job_title']),
            'reset_password'       => boolval($model['reset_password'] ?? false),
            'outsourcing_category' => isset($model['outsourcing_category']) ? intval($model['outsourcing_category']) : null,
            'instructor_id'        => isset($model['instructor_id']) ? intval($model['instructor_id']) : null,
            'superior_id'          => isset($model['manager']) ? intval($model['manager']) : null,
            'bank_no'              => isset($model['bank_no']) ? strval($model['bank_no']) : null,
            'bank_type'            => intval($model['bank_type'] ?? 0),
            'bank_no_name'         => isset($model['bank_no_name']) ? strval($model['bank_no_name']) : null,
            'hire_type'            => intval($model['hire_type'] ?? 0),
            'manage_store'         => [],
            'company_type' => StaffService::getInstance()->isLnt($model['contract_company_id']) ? CompanyService::MS_COMPANY_ENUM_LNT : CompanyService::MS_COMPANY_ENUM_FLASH,
            //公司类型
        ];

        //高级主管同步管辖网点
        if (isCountry('TH') && $model['job_title'] == JobTitleEnums::TH_SENIOR_BRANCH_SUPERVISOR) {
            $manageStore                 = StaffRelationsService::getInstance()->getStaffStores($model['staff_info_id'],
                StaffRelationsService::TYPE_STAFF_MANAGEMENT);
            $messageBody['manage_store'] = $manageStore ? array_column($manageStore, 'id') : [];
        }

        if (env('break_away_from_ms')) {
            return $this->saveStaffInfoToFle($messageBody);
        }

        $mq                           = new RocketMQ('sync-staff-to-java');
        $sendData['jsonCondition']    = json_encode($messageBody, JSON_UNESCAPED_UNICODE);
        $sendData['handleType']       = RocketMQ::TAG_HR_STAFF_SAVE;
        $sendData['shardingOrderKey'] = $model['staff_info_id'];
        $mq->setShardingKey($model['staff_info_id']);
        $result                       = $mq->sendOrderlyMsg($sendData,3);
        return !empty($result);
    }

    /**
     * 员工在职装改变化生产者
     * @param $staffId
     * @param $before
     * @param $after
     * @return void
     */
    public function producerStaffOnJobChange($staffId, $before, $after)
    {
        if (!($before['state'] != $after['state'] || $before['wait_leave_state'] != $after['wait_leave_state'])) {
            return;
        }
        $messageBody      = [
            'state_date'    => gmdate('Y-m-d',time()+TIME_ADD_HOUR*3600),
            'staff_info_id' => $staffId,
            'before'        => $before,
            'after'         => $after,
            'created_at'    => gmdate('Y-m-d H:i:s'),
        ];
        $mq               = new RocketMQ('staff-on-job-change');
        $sendData['data'] = $messageBody;
        $sendData['type'] = RocketMQ::TAG_NAME_STAFF_ON_JOB_CHANGE;
        $mq->setShardingKey($staffId);
        $result = $mq->sendOrderlyMsg($sendData,10);
        Yii::$app->logger->write_log([$messageBody, $result], 'info');
    }
    /**
     * @param $staffInfo
     * @return true
     */
    protected function saveStaffInfoToFle($staffInfo)
    {
        $model = StaffInfo::find()->where(['id' => $staffInfo['id']])->one();
        if (empty($model)) {
            $model                       = new StaffInfo();
            $model->id                   = $staffInfo['id'];
            $staffInfo['reset_password'] = true;
        }
        $model->company_name         = $staffInfo['company_name'];
        $model->name                 = $staffInfo['name'];
        $model->mobile               = $staffInfo['mobile'];
        $model->email                = $staffInfo['email'];
        $model->personal_email       = $staffInfo['personal_email'];
        $model->vehicle              = $staffInfo['vehicle'];
        $model->formal               = $staffInfo['formal'];
        $model->state                = $staffInfo['state'];
        $model->wait_leave_state     = $staffInfo['wait_leave_state'];
        $model->leave_date           = $staffInfo['leave_date'];
        $model->hire_date            = $staffInfo['hire_date'];
        $model->organization_id      = $staffInfo['organization_id'];
        $model->organization_type    = $staffInfo['organization_type'];
        $model->department_id        = strval($staffInfo['department_id']);
        $model->outsourcing_pay_type = StaffItems::OS_PAY_TYPE_MAP[$staffInfo['outsourcing_pay_type']]??null;
        $model->profile_photo_path   = $staffInfo['profile_photo_path'] ?? '';
        $model->is_sub_staff         = $staffInfo['is_sub_staff'];
        $model->master_staff         = $staffInfo['master_staff'];
        $model->stop_duties_date     = $staffInfo['stop_duties_date'];
        $model->job_title            = $staffInfo['job_title'];
        $model->outsourcing_category = $staffInfo['outsourcing_category'];
        $model->mobile_company       = $staffInfo['mobile_company'];
        $model->instructor_id        = $staffInfo['instructor_id'];
        $model->superior_id          = $staffInfo['superior_id'];
        $model->bank_no              = $staffInfo['bank_no'];
        $model->bank_type            = $staffInfo['bank_type'];
        $model->bank_no_name         = $staffInfo['bank_no_name'];
        $model->hire_type            = $staffInfo['hire_type'];
        $model->save();
        if (!empty($model->getErrors())) {
            Yii::$app->logger->write_log(['staffInfo' => $staffInfo, 'getErrors' => $model->getErrors()]);
            throw new BusinessException(implode(' ', array_values($model->getErrors())));
        }
        StaffInfoPosition::deleteAll(['staff_info_id' => $staffInfo['id']]);

        if (!empty($staffInfo['positions'])) {
            $insertData = [];
            foreach ($staffInfo['positions'] as $role) {
                $insertData[] = [$staffInfo['id'], $role,];
            }
            Yii::$app->fle->createCommand()->batchInsert(StaffInfoPosition::tableName(),
                ['staff_info_id', 'position_category'], $insertData)->execute();
        }
        //处理重置密码
        if (!empty($staffInfo['reset_password'])) {
            $body   = [
                'jsonrpc' => '2.0',
                'method'  => 'reset_password',
                'params'  => [
                    ['locale' => 'en'],
                    ['staff_info_id' => $staffInfo['id']],
                ],
                'id'      => time(),
            ];
            $res    = Yii::$app->http->json_rpc_post(Yii::$app->params['hcm_rpc'], $body);
            $resObj = json_decode($res, true);
            if (empty($resObj['result'])) {
                Yii::$app->logger->write_log('重置密码：' . $staffInfo['id'] . ' result ' . json_encode($res,
                        JSON_UNESCAPED_UNICODE));
            }
        }
        Yii::$app->logger->write_log('员工信息同步成功；' . json_encode($staffInfo, JSON_UNESCAPED_UNICODE), 'info');
        return true;
    }


}

