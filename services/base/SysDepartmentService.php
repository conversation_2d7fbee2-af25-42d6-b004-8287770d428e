<?php

namespace app\services\base;

use app\libs\Enums\ShiftEnums;
use app\models\backyard\SysDepartment;
use app\modules\v1\business\SysGroupDeptV2;

class SysDepartmentService extends BaseService
{

    /**
     * @param array $field --字段
     * @param string|null $column_key -- 返回的字段 全部给null
     * @param array |null $where
     * @return array
     */
    public function getDataUseIdIndex(
        array $field = ['id', 'name'],
        ?string $column_key = 'name',
        ?array $where = []
    ): array {
        $sysDepartment = SysDepartment::find()->select($field);
        if ($where) {
            $sysDepartment->where($where);
        }
        $data = $sysDepartment->asArray()->all();
        return array_column($data, $column_key, 'id');
    }

    /**
     * 获取部门列表
     * @param string $query_name
     * @param array $field
     * @param array $where
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getList(
        string $query_name = '',
        array $field = ['id', 'name'],
        array $where = [],
        $limit = 30
    ) {
        $sysDepartment = SysDepartment::find()->select($field);
        $notIn[]       = 6;
        if (YII_COUNTRY != 'ID') {
            $notIn[] = 2;
        }
        $sysDepartment->where(["not in", "id", $notIn]);

        if ($query_name) {
            $sysDepartment->andWhere(['LIKE', 'name', '%'.$query_name.'%', false]);
        }
        if ($where) {
            $sysDepartment->andWhere($where);
        }
        if (!empty($limit)) {
            $sysDepartment->limit($limit);
        }
        return $sysDepartment->orderBy(['name' => SORT_ASC])
            ->asArray()
            ->all();
    }

    /**
     * 根据 传参 is_sub_department 取决于是否查询子部门
     * @param $whereDepartment
     * @return bool
     */
    public static function getSubDepartmentByIsSub(&$whereDepartment, $params = []): bool
    {
        //$is_sub_department = ShiftEnums::QUERY_SUB_DEPARTMENT; // 表示是否查询子部门，0 不查询，1 需要查询，默认是查询子部门的
        if (is_null($whereDepartment)) {
            return false;
        }

        $is_sub_department = $params['is_sub_department'] ?? ShiftEnums::QUERY_SUB_DEPARTMENT;
        if (is_numeric($is_sub_department) && in_array($is_sub_department,
                [ShiftEnums::DO_NOT_QUERY_SUB_DEPARTMENTS, ShiftEnums::QUERY_SUB_DEPARTMENT])) {
            $is_sub_department = (int)$is_sub_department;
        } else {
            $is_sub_department = ShiftEnums::QUERY_SUB_DEPARTMENT;
        }

        // 不需要查询子部门时 直接返回
        if (ShiftEnums::QUERY_SUB_DEPARTMENT !== $is_sub_department) {
            return true;
        }

        // 需要查询子部门 且 查询GROUP CEO时 返回false，方法调用处，则不再拼接 node_department_id = department_id 的where条件
        if (999 == $whereDepartment) {
            $whereDepartment = [];
            return false;
        }

        $dept_detail = SysDepartment::find()
            ->select(['id', 'name', 'ancestry', 'ancestry_v2', 'ancestry_v3', 'type', 'level', 'group_boss_id'])
            ->andWhere(['id' => $whereDepartment])
            ->asArray()
            ->one();
        if (!empty($dept_detail)) {
            $ancestry_v3     = empty($dept_detail['ancestry_v3']) ? $dept_detail['id'] : $dept_detail['ancestry_v3'];
            $dept_list_query = SysDepartment::find()->select([
                'id',
                'name',
                'ancestry',
                'ancestry_v2',
                'ancestry_v3',
                'type',
                'level',
                'group_boss_id',
            ])
                ->andWhere(['LIKE', 'ancestry_v3', $ancestry_v3.'/%', false])
                ->orWhere(['id' => $whereDepartment]);

            $dept_list = $dept_list_query->asArray()->indexBy('id')->all();
            if (!empty($dept_list)) {
                $whereDepartment = array_column($dept_list, 'id');
            }
        }
        return true;
    }

}

