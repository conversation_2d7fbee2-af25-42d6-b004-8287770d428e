import i18n from '@/locale'
import api from '@/libs/api.request'
import { getToken } from '@/libs/util'
import moment from 'moment'
export default {
  getRule($this) {
    const hireDateValidate = (rule, value, callback) => {
      if ($this.staffForm.state == 2 && $this.staffForm.leave_date) {
        const d1 = moment(value, 'YYYY-MM-DD').valueOf()
        const d2 = moment($this.staffForm.leave_date, 'YYYY-MM-DD').valueOf()
        if (d1 > d2) {
          callback('Leave date greater than hire date')
          return
        }
      }
      callback()
    }
    const stopDutiesDateValidate = (rule, value, callback) => {
      if ($this.staffForm.state == 2 && $this.staffForm.leave_date) {
        const d1 = moment(value, 'YYYY-MM-DD').valueOf()
        const d2 = moment($this.staffForm.leave_date, 'YYYY-MM-DD').valueOf()
        if (d1 > d2) {
          callback('stop Duties date greater than hire date')
          return
        }
      }
      callback()
    }
    const LeaveValidate = (rule, value, callback) => {
      if ($this.staffForm.state == 2 && $this.staffForm.leave_date) {
        const d1 = moment($this.staffForm.hire_date, 'YYYY-MM-DD').valueOf()
        const d2 = moment(value, 'YYYY-MM-DD').valueOf()
        if (d1 > d2) {
          callback('Leave date greater than hire date')
          return
        }
      }
      callback()
    }

    const ldRequire = ()=>{
      return $this.staffForm.state
    }
    return {
      email: [
        { type: 'email', max: 50, message: 'Incorrect email format', trigger: 'blur' }
      ],
      emp_id: [
        { type: 'string', message: 'The identity cannot be HRID', trigger: 'blur', pattern: /^([0-9a-zA-Z]{2,10})?$/ }
      ],
      name: [
        { required: true, message: 'The name cannot be empty', trigger: 'blur' },
        { type: 'string', min: 1, max: 50, message: 'name max 50 character', trigger: 'blur' }
      ],
      name_en: [
        { type: 'string', max: 50, message: 'name_en max 50 characters', trigger: 'blur' }
      ],
      sex: [
        { required: false, message: 'The name cannot be empty', trigger: 'blur' }
      ],
      state: [
        { required: true, message: 'The state cannot be empty', trigger: 'blur' }
      ],
      payment_markup: [
        {type: 'number', required: true, message: 'cannot be empty', trigger: 'blur' }
      ],
      payment_state: [
        { required: true, message: 'Please select payment state', trigger: 'change' }
      ],
      identity: [
        { required: true, message: 'The identity is invalid', trigger: 'blur', pattern: /^([0-9a-zA-Z]{8,13})?$/ }
      ],
      mobile: [
        { required: true, message: 'The Mobile Number must be 10 digits', trigger: 'blur', pattern: /^([0-9]{10,10})?$/ }
      ],
      sys_department_id: [
        { required: true, message: 'The department cannot be empty', trigger: 'blur' }
      ],
      manager: [
        { required: true, message: 'The manager cannot be empty', trigger: 'blur' }
      ],
      leave_reason: [
        {  type: 'number', required: true, message: 'The leave reson cannot be empty', trigger: 'blur' }
      ],
      job_title: [
        { required: true, message: 'The job title cannot be empty', trigger: 'blur' }
      ],
      sys_store_id: [
        { required: true, message: 'The store cannot be empty', trigger: 'blur' }
      ],
      position_category: [
        { required: true, message: 'The role cannot be empty', trigger: 'blur', type: 'array' }
      ],
      shiftType: [
        { required: true, message: 'The shift Type cannot be empty', trigger: 'change' }
      ],
      workTime: [
        { type: 'number', required: true, message: 'The work Time cannot be empty', trigger: 'blur' }
      ],
      formal: [
        { required: true, message: 'The formal cannot be empty', trigger: 'blur' }
      ],
      stop_duties_date: [
        { required: true, message: 'The stop duties date cannot be empty' },
        { validator: stopDutiesDateValidate, trigger: 'blur' },
      ],
      hire_date: [
        { required: true, message: 'The hire date cannot be empty' },
        { validator: hireDateValidate, trigger: 'blur' },
      ],
      leave_date: [
        { required: true, message: 'date cannot be empty' },
        { validator: LeaveValidate, trigger: 'blur' },
      ],
      // 外协
      pay_type: [
        { required: true, message: i18n.t('pay_type_tip') }
      ],
    }
  },
  sub_staffs_header: [{
      title: i18n.t('staff_no'),
      key: 'staff_info_id',
      width: 100,
    },
    {
      title: i18n.t('name'),
      width: 200,
      key: 'name'
    },
    {
      title: i18n.t('department'),
      width: 150,
      key: 'sys_department_name',
    },
    {
      title: i18n.t('job_title'),
      key: 'job_title_name',
      // minWidth: 170,
      tooltip: true,
      renderHeader: (h, index) => {
        return h('strong', i18n.t('job_title'))
      }
    },
    {
      title: i18n.t('store'),
      tooltip: true,
      width: 200,
      key: 'sys_store_name',
      renderHeader: (h, index) => {
        return h('strong', i18n.t('store'))
      }
    },
    {
      title: i18n.t('state'),
      key: 'state',
      width: 90,
      render: (h, params) => {
        let s = params.row.state == 1 ? i18n.t('state_1') : i18n.t('state_2')
        return h('div', [
          h('Icon', {
            props: {
              type: 'person'
            }
          }),
          h('strong', s)
        ])
      },
    }
  ],
  operation_record_header: [
    {
      title: '#',
      type: 'index',
      width: 100
    },
    {
      title: i18n.t('operation_time'),
      width: 200,
      render: (h, params) => {
        return h('span', params.row.change[0].substring(5))
      }
    },
    {
      title: i18n.t('operation_record'),
      key: 'name',
      render: (h, params) => {
        const recordArr = params.row.change.slice(7)
        return h('span', recordArr.join('; '))
      }
    },
    {
      title: i18n.t('operator'),
      width: 200,
      render: (h, params) => {
        const change = params.row.change
        const value = change[1].substring(5) + ' ' + change[2].substring(5) + ' （' + change[4].substring(7) + '）'
        return h('span', value)
      }
    }
  ],
  tableHeader($this) {
    return [{
        title: i18n.t('staff_no'),
        key: 'staff_info_id',
        fixed: 'left',
        width: 76,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('staff_no'))
        }
      },
      {
        title: 'HR ID',
        width: 80,
        key: 'emp_id'
      },
      {
        title: i18n.t('name'),
        width: 120,
        key: 'name',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('name'))
        }
      },
      {
        title: i18n.t('name_en'),
        width: 120,
        key: 'name_en',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('name_en'))
        }
      },
      {
        title: i18n.t('job_title'),
        key: 'job_title_name',
        width: 120,
        tooltip: true,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('job_title'))
        }
      },
      {
        title: i18n.t('department'),
        width: 150,
        key: 'sys_department_name',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('department'))
        }
      },
      {
        title: i18n.t('area'),
        width: 150,
        tooltip: true,
        key: 'store_area_id',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('area'))
        },
        render: (h, params) => {
          if (params.row.store_area_id) {
            const value = $this.sysinfo.area_name_list.find(item => {
              return item.key === params.row.store_area_id
            }).value
            return h('span', value)
          } else {
            return h('span', '-')
          }
        }
      },
      {
        title: i18n.t('store'),
        width: 150,
        key: 'sys_store_name',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('store'))
        }
      },
      {
        title: i18n.t('post_label'),
        key: 'position_category',
        width: 170,
        render: (h, params) => {
          let s = params.row.position_category
          let d = (s || []).map((i) => {
            return i18n.t('role_'+ i) + ' '
          })
          return h('div', [
            h('Icon', {
              props: {
                type: 'person'
              }
            }),
            h('strong', d)
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('post_label'))
        }
      },
      // {
      //   title: i18n.t('mobile'),
      //   width: 120,
      //   key: 'mobile',
      //   renderHeader: (h, index) => {
      //     return h('strong', i18n.t('mobile'))
      //   }
      // },
      {
        title: i18n.t('imm_supervisor'),
        key: 'manager',
        minWidth: 120,
        tooltip: true,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('imm_supervisor'))
        }
      },
      {
        title: i18n.t('state'),
        key: 'state',
        minWidth: 120,
        render: (h, params) => {
          let s = params.row.state == 1 ? i18n.t('state_1') : params.row.state == 2 ? i18n.t('state_2') : i18n.t('state_3')
          return h('div', [
            h('Icon', {
              props: {
                type: 'person'
              }
            }),
            h('strong', s)
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('state'))
        }
      },
      {
        title: i18n.t('hire_date'),
        key: 'hire_date',
        minWidth: 120,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('hire_date'))
        },
        render: (h, params) => {
          let text = '-'
          if (params.row.hire_date) {
            text = params.row.hire_date.substring(0, 10)
          }
          return h('span', text)
        }
      },
      {
        title: i18n.t('leave_date'),
        key: 'leave_date',
        minWidth: 120,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('leave_date'))
        },
        render: (h, params) => {
          let text = '-'
          if (params.row.state === '2') {
            text = params.row.leave_date.substring(0, 10)
          }
          return h('span', text)
        }
      },
      {
        title: i18n.t('suspension_date'),
        key: 'stop_duties_date',
        minWidth: 120,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('suspension_date'))
        },
        render: (h, params) => {
          let text = '-'
          if (params.row.state === '3') {
            text = params.row.stop_duties_date.substring(0, 10)
          }
          return h('span', text)
        }
      },
      {
        title: i18n.t('operation'),
        key: 'address',
        fixed: 'right',
        minWidth: 160,
        render: (h, params) => {
          return h('ButtonGroup', [
            h('Button', {
              props: {
                size: 'small',
                icon: 'ios-create-outline'
              },
              on: {
                click: () => {
                  $this.loadStaff(params.row.staff_info_id, 'edit')
                  return false
                }
              }
            }, i18n.t('from_edit')),
            h('Button', {
              props: {
                size: 'small',
                icon: 'ios-eye'
              },
              on: {
                click: () => {
                  $this.loadStaff(params.row.staff_info_id, 'info')
                  return false
                }
              }
            }, i18n.t('from_info')),
            // h('Button', {
            //   props: {
            //     size: 'small',
            //     icon: 'ios-eye'
            //   },
            //   on: {
            //     click: () => {
            //       $this.onBtnRecord(params.row.staff_info_id)
            //       return false
            //     }
            //   }
            // }, i18n.t('from_record'))
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('operation'))
        }
      }
    ]
  },
  //邮箱管理
  tableHeaderM($this) {
    return [{
      width: 50,
      type: 'selection',
    },{
        title: i18n.t('staff_no'),
        key: 'staff_info_id',
        // fixed: 'left',
        width: 76,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('staff_no'))
        }
      },
      {
        title: i18n.t('name'),
        width: 120,
        key: 'name',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('name'))
        }
      },
      {
        title: i18n.t('name_en'),
        width: 120,
        key: 'name_en',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('name_en'))
        }
      },
      {
        title: i18n.t('department'),
        width: 150,
        key: 'sys_department_name',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('department'))
        }
      },
      {
        title: i18n.t('job_title'),
        key: 'job_title_name',
        width: 120,
        tooltip: true,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('job_title'))
        }
      },
      {
        title: i18n.t('state'),
        key: 'staff_state',
        minWidth: 120,
        render: (h, params) => {
          let s = params.row.staff_state == 1 ? i18n.t('state_1') : params.row.staff_state == 2 ? i18n.t('state_2') : i18n.t('state_3')
          return h('div', [
            h('Icon', {
              props: {
                type: 'person'
              }
            }),
            h('strong', s)
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('state'))
        }
      },
      {
        title: i18n.t('hire_date'),
        width: 200,
        key: 'hire_date',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('hire_date'))
        }
      },
      {
        title: i18n.t('leave_date'),
        width: 200,
        key: 'leave_date',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('leave_date'))
        }
      },
      {
        title: i18n.t('email'),
        width: 120,
        key: 'email',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('email'))
        }
      },
      {
        title: i18n.t('mailbox_status'),
        key: 'state',
        minWidth: 120,
        render: (h, params) => {
          let s = params.row.state == 0 ? i18n.t('turned_0') : params.row.state == 1 ? i18n.t('turned_1') : i18n.t('turned_2')
          return h('div', [
            h('Icon', {
              props: {
                type: 'person'
              }
            }),
            h('strong', s)
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('mailbox_status'))
        }
      },
      {
        title: i18n.t('operation'),
        key: 'address',
        // fixed: 'right',
        width: 160,
        render: (h, params) => {
          return h('ButtonGroup', [
            h('Button', {
              props: {
                size: 'small',
                icon: 'ios-create-outline'
              },
              style: {
                display: (params.row.state == 1 && params.row.staff_state == 2) ? 'inline-block' : 'none'
              },
              on: {
                click: () => {
                  let arr1 = []
                  arr1[0] = params.row.staff_info_id
                  $this.loadStaff(arr1, '1')
                  return false
                }
              }
            }, i18n.t('logged')),
            h('Button', {
              props: {
                size: 'small',
                icon: 'ios-eye'
              },
              style: {
                display: (params.row.state == 0 && params.row.staff_state == 1) ? 'inline-block' : 'none'
              },
              on: {
                click: () => {
                  let arr = []
                  arr[0] = params.row.staff_info_id
                  $this.loadStaff(arr, '0')
                  console.log('2312321')
                  return false
                }
              }
            }, i18n.t('opened'))
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('operation'))
        }
      }
    ]
  },
  // 员工班次设置
   tableHeaderSH($this) {
    return [{
      width: 50,
      type: 'selection',
    },{
        title: i18n.t('staff_no'),
        key: 'staff_info_id',
        // fixed: 'left',
        width: 76,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('staff_no'))
        }
      },
      {
        title: i18n.t('name'),
        width: 120,
        key: 'name',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('name'))
        }
      },
      {
        title: i18n.t('department'),
        width: 150,
        key: 'sys_department_name',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('department'))
        }
      },
      {
        title: i18n.t('job_title'),
        key: 'job_title_name',
        width: 120,
        tooltip: true,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('job_title'))
        }
      },
      {
        title: i18n.t('store'),
        width: 150,
        key: 'sys_store_name',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('store'))
        }
      },
      {
        title: i18n.t('post_label'),
        key: 'position_category',
        width: 170,
        render: (h, params) => {
          let s = params.row.position_category
          let d = (s || []).map((i) => {
            return i18n.t('role_'+ i) + ' '
          })
          return h('div', [
            h('Icon', {
              props: {
                type: 'person'
              }
            }),
            h('strong', d)
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('post_label'))
        }
      },
      {
        title: i18n.t('state'),
        key: 'state',
        width: 120,
        render: (h, params) => {
          let s = params.row.state == 1 ? i18n.t('state_1') : params.row.state == 2 ? i18n.t('state_2') : i18n.t('state_3')
          return h('div', [
            h('Icon', {
              props: {
                type: 'person'
              }
            }),
            h('strong', s)
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('state'))
        }
      },
      {
        title: i18n.t('shift_type'),
        width: 150,
        key: 'shift_type',
        render: (h, params) => {
          let s = params.row.shift_type == 'EARLY' ? i18n.t('morning_shift') : params.row.shift_type == 'MIDDLE' ? i18n.t('afternoon_shift') : params.row.shift_type == 'NIGHT' ? i18n.t('night_shift') : ''
          return h('div', [
            h('Icon', {
              props: {
                type: 'person'
              }
            }),
            h('strong', s)
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('shift_type'))
        }
      },
      {
        title: i18n.t('work_time'),
        minWidth: 150,
        key: 'workTime',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('work_time'))
        }
      },
      {
        title: i18n.t('operation'),
        key: 'address',
        // fixed: 'right',
        width: 160,
        render: (h, params) => {
          return h('ButtonGroup', [
            h('Button', {
              props: {
                size: 'small',
                icon: 'ios-create-outline'
              },
              style: {
                display: (params.row.state == 1) ? 'inline-block' : 'none'
              },
              on: {
                click: () => {
                  let arr1 = []
                  arr1[0] = {id: params.row.staff_info_id, name: params.row.name }
                  $this.loadStaff(arr1, '1')
                  return false
                }
              }
            }, i18n.t('from_edit')),
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('operation'))
        }
      }
    ]
  },
  // 薪资管理
  tableHeaderS($this, salaryOr) {
    return [{
        title: i18n.t('staff_no'),
        key: 'staff_info_id',
        fixed: 'left',
        width: 76,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('staff_no'))
        }
      },
      {
        title: i18n.t('name'),
        width: 120,
        key: 'name',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('name'))
        }
      },
      {
        title: i18n.t('department'),
        width: 150,
        key: 'sys_department_name',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('department'))
        }
      },
      {
        title: i18n.t('job_title'),
        key: 'job_title_name',
        width: 120,
        tooltip: true,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('job_title'))
        }
      },
      {
        title: i18n.t('store'),
        width: 150,
        key: 'sys_store_name',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('store'))
        }
      },
      {
        title: i18n.t('post_label'),
        key: 'position_category',
        width: 170,
        render: (h, params) => {
          let s = params.row.position_category
          let d = (s || []).map((i) => {
            return i18n.t('role_'+ i) + ' '
          })
          return h('div', [
            h('Icon', {
              props: {
                type: 'person'
              }
            }),
            h('strong', d)
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('post_label'))
        }
      },
      {
        title: i18n.t('state'),
        key: 'state',
        minWidth: 120,
        render: (h, params) => {
          let s = params.row.state == 1 ? i18n.t('state_1') : params.row.state == 2 ? i18n.t('state_2') : i18n.t('state_3')
          return h('div', [
            h('Icon', {
              props: {
                type: 'person'
              }
            }),
            h('strong', s)
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('state'))
        }
      },
      {
        title: i18n.t('reular_wage'),
        width: 150,
        key: 'base_salary',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('reular_wage'))
        }
      },
      {
        title: i18n.t('experience_allowance'),
        width: 150,
        key: 'exp_allowance',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('experience_allowance'))
        }
      },
      {
        title: i18n.t('post_allowance'),
        width: 150,
        key: 'position_allowance',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('post_allowance'))
        }
      },
      {
        title: i18n.t('car_allowance'),
        width: 150,
        key: 'car_rental',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('car_allowance'))
        }
      },
      {
        title: i18n.t('oil_supplement'),
        width: 150,
        key: 'trip_payment',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('oil_supplement'))
        }
      },
      {
        title: i18n.t('computer_subsidy'),
        width: 150,
        key: 'notebook_rental',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('computer_subsidy'))
        }
      },
      {
        title: i18n.t('recommended_subsidy'),
        width: 150,
        key: 'recommended',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('recommended_subsidy'))
        }
      },
      {
        title: i18n.t('meal_allowance'),
        width: 150,
        key: 'food_allowance',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('meal_allowance'))
        }
      },
      {
        title: i18n.t('danger_zone'),
        width: 150,
        key: 'dangerous_area',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('danger_zone'))
        }
      },
      {
        title: i18n.t('operation'),
        key: 'address',
        fixed: 'right',
        minWidth: 160,
        render: (h, params) => {
          return h('ButtonGroup', [
            h('Button', {
              props: {
                size: 'small',
                icon: 'ios-create-outline'
              },
              attrs: {
                disabled: salaryOr
              },
              on: {
                click: () => {
                  $this.loadStaff(params.row.staff_info_id, 'edit', params.row.name, params.row.staff_info_id)
                  return false
                }
              }
            }, i18n.t('from_edit')),
            h('Button', {
              props: {
                size: 'small',
                icon: 'ios-eye'
              },
              on: {
                click: () => {
                  $this.loadStaff(params.row.staff_info_id, 'info', params.row.name, params.row.staff_info_id)
                  console.log('2312321')
                  return false
                }
              }
            }, i18n.t('from_info'))
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('operation'))
        }
      }
    ]
  },
  tableHeaderD($this) {
    return [{
        title: i18n.t('staff_no'),
        key: 'staff_info_id',
        fixed: 'left',
        width: 76,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('staff_no'))
        }
      },
      {
        title: 'HR ID',
        width: 80,
        key: 'emp_id'
      },
      {
        title: i18n.t('name'),
        width: 120,
        key: 'name',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('name'))
        }
      },
      {
        title: i18n.t('name_en'),
        width: 120,
        key: 'name_en',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('name_en'))
        }
      },
      {
        title: i18n.t('job_title'),
        key: 'job_title_name',
        width: 120,
        tooltip: true,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('job_title'))
        }
      },
      {
        title: i18n.t('area'),
        width: 150,
        tooltip: true,
        key: 'store_area_id',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('area'))
        },
        render: (h, params) => {
          if (params.row.store_area_id) {
            const value = $this.sysinfo.area_name_list.find(item => {
              return item.key === params.row.store_area_id
            }).value
            return h('span', value)
          } else {
            return h('span', '-')
          }
        }
      },
      {
        title: i18n.t('store'),
        width: 150,
        key: 'sys_store_name',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('store'))
        }
      },
      {
        title: i18n.t('post_label'),
        key: 'position_category',
        width: 170,
        render: (h, params) => {
          let s = params.row.position_category
          let d = (s || []).map((i) => {
            return i18n.t('role_'+ i) + ' '
          })
          return h('div', [
            h('Icon', {
              props: {
                type: 'person'
              }
            }),
            h('strong', d)
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('post_label'))
        }
      },
      // {
      //   title: i18n.t('mobile'),
      //   width: 120,
      //   key: 'mobile',
      //   renderHeader: (h, index) => {
      //     return h('strong', i18n.t('mobile'))
      //   }
      // },
      {
        title: i18n.t('imm_supervisor'),
        key: 'manager',
        minWidth: 120,
        tooltip: true,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('imm_supervisor'))
        }
      },
      {
        title: i18n.t('state'),
        key: 'state',
        minWidth: 120,
        render: (h, params) => {
          let s = params.row.state == 1 ? i18n.t('state_1') : params.row.state == 2 ? i18n.t('state_2') : i18n.t('state_3')
          return h('div', [
            h('Icon', {
              props: {
                type: 'person'
              }
            }),
            h('strong', s)
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('state'))
        }
      },
      {
        title: i18n.t('hire_date'),
        key: 'hire_date',
        minWidth: 120,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('hire_date'))
        },
        render: (h, params) => {
          let text = '-'
          if (params.row.hire_date) {
            text = params.row.hire_date.substring(0, 10)
          }
          return h('span', text)
        }
      },
      {
        title: i18n.t('leave_date'),
        key: 'leave_date',
        minWidth: 120,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('leave_date'))
        },
        render: (h, params) => {
          let text = '-'
          if (params.row.state === '2') {
            text = params.row.leave_date.substring(0, 10)
          }
          return h('span', text)
        }
      },
      {
        title: i18n.t('suspension_date'),
        key: 'stop_duties_date',
        minWidth: 120,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('suspension_date'))
        },
        render: (h, params) => {
          let text = '-'
          if (params.row.state === '3') {
            text = params.row.stop_duties_date.substring(0, 10)
          }
          return h('span', text)
        }
      },
      {
        title: i18n.t('operation'),
        key: 'address',
        fixed: 'right',
        minWidth: 160,
        render: (h, params) => {
          return h('ButtonGroup', [
            h('Button', {
              props: {
                size: 'small',
                icon: 'ios-create-outline'
              },
              on: {
                click: () => {
                  $this.loadStaff(params.row.staff_info_id, 'edit')
                  return false
                }
              }
            }, i18n.t('from_edit')),
            h('Button', {
              props: {
                size: 'small',
                icon: 'ios-eye'
              },
              on: {
                click: () => {
                  $this.loadStaff(params.row.staff_info_id, 'info')
                  console.log('2312321')
                  return false
                }
              }
            }, i18n.t('from_info'))
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('operation'))
        }
      }
    ]
  },
  tableHeaderG($this) {
    return [{
        title: i18n.t('staff_no'),
        key: 'staff_info_id',
        fixed: 'left',
        width: 76,
        renderHeader: (h, index) => {
          return h('strong', i18n.t('staff_no'))
        }
      },
      {
        title: i18n.t('name'),
        width: 120,
        key: 'name',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('name'))
        }
      },
      // {
      //   title: i18n.t('mobile'),
      //   width: 120,
      //   key: 'mobile',
      //   renderHeader: (h, index) => {
      //     return h('strong', i18n.t('mobile'))
      //   }
      // },
      {
        title: i18n.t('store'),
        width: 150,
        key: 'sys_store_name',
        renderHeader: (h, index) => {
          return h('strong', i18n.t('store'))
        }
      },
      {
        title: i18n.t('post_label'),
        key: 'position_category',
        width: 170,
        render: (h, params) => {
          let s = params.row.position_category
          let d = (s || []).map((i) => {
            return i18n.t('role_'+ i) + ' '
          })
          return h('div', [
            h('Icon', {
              props: {
                type: 'person'
              }
            }),
            h('strong', d)
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('post_label'))
        }
      },
      {
        title: i18n.t('status'),
        key: 'state',
        width: 100,
        render: (h, params) => {
          let s = params.row.state == 1 ? i18n.t('state_11') : i18n.t('state_22')
          return h('div', [
            h('Icon', {
              props: {
                type: 'person'
              }
            }),
            h('strong', s)
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('status'))
        }
      },
      {
        title: i18n.t('cooperator_type'),
        key: 'formal',
        minWidth: 170,
        render: (h, params) => {
          let s = params.row.formal == 2 ? 'Flash Home' : i18n.t('others')
          return h('div', [
            h('Icon', {
              props: {
                type: 'person'
              }
            }),
            h('strong', s)
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('cooperator_type'))
        }
      },
      {
        title: i18n.t('operation'),
        key: 'address',
        fixed: 'right',
        minWidth: 160,
        render: (h, params) => {
          return h('ButtonGroup', [
            h('Button', {
              props: {
                size: 'small',
                icon: 'ios-create-outline'
              },
              on: {
                click: () => {
                  $this.loadStaff(params.row.staff_info_id, 'edit')
                  return false
                }
              }
            }, i18n.t('from_edit')),
            h('Button', {
              props: {
                size: 'small',
                icon: 'ios-eye'
              },
              on: {
                click: () => {
                  $this.loadStaff(params.row.staff_info_id, 'info')
                  return false
                }
              }
            }, i18n.t('from_info'))
          ])
        },
        renderHeader: (h, index) => {
          return h('strong', i18n.t('operation'))
        }
      }
    ]
  },
  exportExcel($this) {
    let query = []
    for (let i in $this.search) {
      query.push(i + '=' + $this.search[i])
    }
    query.push('lang=' + $this.$i18n.locale)
    query.push('token=' + getToken())
    console.log(query)
    let href = api.baseUrl + 'staffs/export?' + query.join('&')
    // window.location.href = api.baseUrl + 'staffs/export?' + query.join('&')
    api.request({
      url: href,
      method: 'get',
      responseType: 'blob',
    }).then(res => {
      if(res.status == 200) {
        $this.loadingExl = false
        console.log(1111);
        const blob = new Blob([res.data], { type: 'text/csv' });
        // const link = document.createElement('a');
        const link = document.getElementById('downloadLink'); // 兼容火狐浏览器，不要生成a标记，预先写一个a标记，隐藏。
        link.href = window.URL.createObjectURL(blob);
        if($this.exl_index == 'flash') {
          link.download = 'EmployeeInfo.csv';
        } else if($this.exl_index == 'flash_out') {
          link.download = 'OutSourceEmployeeInfo.csv'
        } else if($this.exl_index == 'cooperative') {
          link.download = 'OfficalPartnerEmployeeInfo.csv'
        }
        link.click();
      }
    }).catch(err => {
      $this.loadingExl = false
    })
  },
  load(params) {
    return new Promise((resolve, reject) => {
      api.request({
        url: 'staffs',
        params: params,
        method: 'get'
      }).then(res => {
        resolve(res.data.body)
      }).catch(err => {
        reject(err)
      })
    })
  }
}
