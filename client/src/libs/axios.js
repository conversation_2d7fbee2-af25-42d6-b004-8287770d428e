import axios from 'axios'
import store from '@/store'
import { getToken, localRead } from '@/libs/util'
import { Message } from 'iview'
import i18n from '@/locale'
const addErrorLog = errorInfo => {
  const { statusText, status, request: { responseURL } } = errorInfo
  let info = {
    type: 'ajax',
    code: status,
    mes: statusText,
    url: responseURL
  }
  if (!responseURL.includes('save_error_logger')) store.dispatch('addErrorLog', info)
}
let arri = 0;

class HttpRequest {
  constructor(baseUrl = baseURL) {
    this.baseUrl = baseUrl
    this.queue = {}
  }
  getInsideConfig() {
    const config = {
      baseURL: this.baseUrl,
      headers: {
        Authorization: 'Bearer ' + getToken(),
        'Cache-Control': 'no-cache',
      }
    }
    return config
  }
  destroy(url) {
    delete this.queue[url]
    if (!Object.keys(this.queue).length) {
      // Spin.hide()
    }
  }
  interceptors(instance, url) {
    // 请求拦截
    instance.interceptors.request.use(config => {
      // 添加全局的loading...
      if (!Object.keys(this.queue).length) {
        // Spin.show() // 不建议开启，因为界面不友好
      }
      this.queue[url] = true
      return config
    }, error => {
      return Promise.reject(error)
    })
    // 响应拦截
    instance.interceptors.response.use(res => {
      this.destroy(url)
      if (res.data.code == 500) {
        return Promise.reject(res.data.msg)
      }

      if(res.data.code == 401){
        store.commit('setToken', '')
        Message.error('Please Refresh The Page')
        return
      }
      if(res.data.version > 190531 && arri == 0){
        Message.info({
          content: 'Please flush browser cache !!',
          duration: 5
        })
        arri = 1
      }
      if(res.data.code != 0 && res.data.code != null && res.data.code != 2){
         Message.error(i18n.t(res.data.msg[0])+' '+i18n.t(res.data.msg[1]||''))
         return Promise.reject(i18n.t(res.data.msg[0])+' '+i18n.t(res.data.msg[1]||''))
      }
      const { data, status } = res
      return { data, status }
    }, error => {
      this.destroy(url)
      let errorInfo = error.response
      if (!errorInfo) {
        const { request: { statusText, status }, config } = JSON.parse(JSON.stringify(error))
        errorInfo = {
          statusText,
          status,
          request: { responseURL: config.url }
        }
      }
      return Promise.reject(error)
    })
  }
  getUrlText(urltext) {
    // flash员工
    if(urltext == 'flash' || urltext == 'batch_create' || urltext == 'batch_upd') {
      return 'formal'
    }
    // 外协员工
    if(urltext == 'flash_out' || urltext == 'batch_create_out') {
      return 'informal'
    }
    //合作商员工
    if(urltext == 'cooperative') {
      return 'partner'
    }
    //下载
    if(urltext == 'flash_download' || urltext == 'batch_create_download') {
      return 'download'
    }
    //实习生员工
    if(urltext == 'trainee' || urltext == 'trainee_create') {
      return 'trainee'
    }
    //网点员工
    if(urltext == 'flash_dot' || urltext == 'dot_create') {
      return 'store'
    }
    //薪资-网点
    if(urltext == 'dot_salary') {
      return 'store_salary'
    }
    //薪资-总部
    if(urltext == 'salary') {
      return 'headoffice_salary'
    }
    if(urltext == 'mailboxManagement') {
      return 'staff_eamil'
    }
    if(urltext == 'shuttle') {
      return 'staff_shift'
    }
    return ''
  }
  request(options) {
    const instance = axios.create()
    if (!options.params) {
      options.params = {}
    }
    console.log('sd', window.location.hash)
    let htmlHash = window.location.hash
    let hashAll = htmlHash.substr(htmlHash.lastIndexOf('/', htmlHash.lastIndexOf('/') - 1) + 1)
		let index = hashAll.lastIndexOf("\/")
    // console.log(hashAll, index)
    let addrLast = decodeURI(hashAll.substring(index + 1, hashAll.length))
    let gethash = this.getUrlText(addrLast)
    const query = JSON.parse(localRead('fbi_from_query'))
    options.params.lang = localRead('local')
    options.params.time = query.time || ''
    options.params.auth = query.auth || ''
    options.params.fbid = query.fbid || ''
    options.params._view = gethash || ''

    options = Object.assign(this.getInsideConfig(), options)
    this.interceptors(instance, options.url)
    return instance(options)
  }
}

export default HttpRequest
