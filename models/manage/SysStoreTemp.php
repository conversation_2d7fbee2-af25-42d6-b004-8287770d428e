<?php

namespace app\models\manage;

use app\models\backyard\SysStore;
use Yii;

/**
 * This is the model class for table "sys_store_temp".
 *
 * @property int $id
 * @property int $sys_store_id 系统网点id
 * @property string $markup_name 网点别名
 * @property string $sys_store_name 系统网点名称
 */
class SysStoreTemp extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'hr_sys_store_temp';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sys_store_id'], 'required'],
            [['sys_store_id'], 'string'],
            [['markup_name'], 'string', 'max' => 32],
            [['sys_store_name'], 'string', 'max' => 50],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sys_store_id' => '系统网点id',
            'markup_name' => '网点别名',
            'sys_store_name' => '系统网点名称',
        ];
    }

    public static function sysStoreExtByStoreId($id)
    {
        static $temp;
        if (!is_null($temp)) {
            return $temp[$id] ?? '';
        }
        $temp = self::find()->select(['sys_store_id', 'markup_name'])->asArray()->all();
        $temp = array_column($temp, 'markup_name', 'sys_store_id');
        return $temp[$id] ?? '';
    }

    /**
     * category:
    1: 收派件网点
    2: 分拨中心
    3: 第三方代理收放网点
    4: 拦站网点
    5: 收派件网点
    6: 加盟商网点
    网点分类 1: 收派件网点 2: 分拨中心 3:第三方代理 4:揽件网点(市场) 5:收派件网点(shop) 6:加盟商网点
     */
    public static function temp()
    {
        return Yii::$app->cache->getOrSet('sys.store.temp', function ($cache) {

            $store = [-1 => [
                'id' => '-1',
                'name' => Yii::$app->lang->get('head_office'),
                'state' => 1,
                'category' => 0,
                'province_code' => '0',
                'sorting_no' => '',
            ]];

            $fle_store_model = SysStore::find()->all();
            foreach ($fle_store_model as $key => $one) {
                if ($one->id == 'TH99999999') {
                    continue;
                }
                $store[$one->id] = [
                    'id' => $one->id,
                    'name' => $one->name,
                    'state' => $one->state,
                    'category' => $one->category,
                    'province_code' => $one->province_code,
                    'sorting_no' => $one->sorting_no,
                ];
            }
            return $store;
        },300);
    }

    public static function storeList($isAllDisplay = 1) {
        return Yii::$app->cache->getOrSet('sys.store.storeList'.$isAllDisplay, function ($cache) use($isAllDisplay) {
            $fle_store_models = SysStore::find()->select(['id','name','state','category','province_code'])->where(['!=', 'category', 6])->andWhere(['NOT IN','id',['TH99999999', 'TH01080102']]);
            if($isAllDisplay == 0) {
                $fle_store_models->andWhere(['state' => 1]);
            }
            $data_rows = $fle_store_models->asArray()->all();
            $data_rows[] = [
                'id' => '-1',
                'name' => Yii::$app->lang->get('head_office'),
                'state' => 1,
                'category' => 0,
                'province_code' => '0',
            ];
            return $data_rows;
        },300);

    }
}
