<?php

namespace app\models\manage;

use Yii;

/**
 * This is the model class for table "salary_transfer".
 *
 * @property string $staff_id 员工id
 * @property string $excel_month 算工资实施的月份2019-05
 * @property string $transfer_day transfer day 2019-05-01
 * @property int $salary_old 单位泰铢
 * @property int $position_old 单位泰铢
 * @property int $experience_old 单位泰铢
 * @property int $house_old 单位泰铢
 * @property int $notebook_old 单位泰铢
 * @property int $car_old 单位泰铢
 * @property int $food_old 单位泰铢
 * @property int $risk_old 单位泰铢
 * @property int $salary_new 单位泰铢
 * @property int $position_new 单位泰铢
 * @property int $experience_new 单位泰铢
 * @property int $house_new 单位泰铢
 * @property int $notebook_new 单位泰铢
 * @property int $car_new 单位泰铢
 * @property int $food_new 单位泰铢
 * @property int $risk_new 单位泰铢
 * @property string $created_at
 */
class SalaryTransfer extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'salary_transfer';
    }
    public static function getDb()
    {
        return Yii::$app->get('backyard_main');
    }
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['staff_id', 'excel_month', 'transfer_day'], 'required'],
            [['staff_id', 'salary_old', 'position_old', 'experience_old', 'house_old', 'notebook_old', 'car_old', 'food_old', 'risk_old', 'salary_new', 'position_new', 'experience_new', 'house_new', 'notebook_new', 'car_new', 'food_new', 'risk_new'], 'integer'],
            [['transfer_day', 'created_at'], 'safe'],
            [['excel_month'], 'string', 'max' => 7],
            [['staff_id', 'excel_month', 'transfer_day'], 'unique', 'targetAttribute' => ['staff_id', 'excel_month', 'transfer_day']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'staff_id' => Yii::t('app', 'Staff ID'),
           'excel_month' => Yii::t('app', 'Excel Month'),
           'transfer_day' => Yii::t('app', 'Transfer Day'),
           'salary_old' => Yii::t('app', 'Salary Old'),
           'position_old' => Yii::t('app', 'Position Old'),
           'experience_old' => Yii::t('app', 'Experience Old'),
           'house_old' => Yii::t('app', 'House Old'),
           'notebook_old' => Yii::t('app', 'Notebook Old'),
           'car_old' => Yii::t('app', 'Car Old'),
           'food_old' => Yii::t('app', 'Food Old'),
           'risk_old' => Yii::t('app', 'Risk Old'),
           'salary_new' => Yii::t('app', 'Salary New'),
           'position_new' => Yii::t('app', 'Position New'),
           'experience_new' => Yii::t('app', 'Experience New'),
           'house_new' => Yii::t('app', 'House New'),
           'notebook_new' => Yii::t('app', 'Notebook New'),
           'car_new' => Yii::t('app', 'Car New'),
           'food_new' => Yii::t('app', 'Food New'),
           'risk_new' => Yii::t('app', 'Risk New'),
           'created_at' => Yii::t('app', 'Created At'),
        ];
    }
}
