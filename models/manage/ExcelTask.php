<?php

namespace app\models\manage;

use Yii;

/**
 * This is the model class for table "excel_task".
 *
 * @property int $id
 * @property string $user_id
 * @property string $executable_path
 * @property string $action_name 任务url，存 php  cli.php  之后的 main   action  ，字符串以逗号隔开
 * @property string $url 下载地址
 * @property int $flag 1=执行完成；0=未完成
 * @property string $action_time 任务执行时间
 * @property string $finish_time 完成时间
 * @property string $args_json
 */
class ExcelTask extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'excel_task';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['flag'], 'integer'],
            [['action_time', 'finish_time'], 'safe'],
            [['user_id'], 'string', 'max' => 30],
            [['executable_path'], 'string', 'max' => 200],
            [['action_name', 'url'], 'string', 'max' => 255],
            [['args_json'], 'string', 'max' => 6500],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'executable_path' => 'Executable Path',
            'action_name' => 'Action Name',
            'url' => 'Url',
            'flag' => 'Flag',
            'action_time' => 'Action Time',
            'finish_time' => 'Finish Time',
            'args_json' => 'Args Json',
        ];
    }
}
