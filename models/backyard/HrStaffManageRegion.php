<?php

namespace app\models\backyard;

use Yii;

/**
 * This is the model class for table "hr_staff_manage_department".
 *
 * @property int $id 主键id
 * @property int $staff_info_id 员工id
 * @property int $region_id 大区 id
 * @property int $deleted 1 删除
 * @property string $created_at 创建时间
 * @property string $updated_at 修改时间
 * @property string $updated_staff 修改人
 * @property string $created_staff 创建人
 * @property int $type 1 管辖范围 2 白名单管辖范围
 */
class HrStaffManageRegion extends \yii\db\ActiveRecord
{

    public static function tableName()
    {
        return 'hr_staff_manage_region';
    }

    public static function getDb()
    {
        return Yii::$app->get('backyard_main');
    }

    public function rules()
    {
        return [
            [["staff_info_id", "region_id"], "required"],
            [['staff_info_id', 'region_id'], 'integer'],
            [['created_at'], 'safe'],
        ];
    }

    public function attributeLabels()
    {
        return [
            "id"            => "primary id",
            "staff_info_id" => "staff info id",
            "region_id"     => "region_id",
            "deleted"       => "deleted",
            "created_at"    => "created at",
            "updated_at"    => "updated at",
            "updated_staff" => "updated_staff",
            'created_staff' => 'created_staff',
            'type'          => 'type',

        ];
    }

}