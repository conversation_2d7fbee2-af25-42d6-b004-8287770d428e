<?php

namespace app\models\backyard;

use Yii;

/**
 * This is the model class for table "hcm_excel_task".
 *
 * @property int $id
 * @property string $staff_info_id 员工工号
 * @property string $executable_path 执行路径
 * @property string $action_name 任务url，存 php  cli.php  之后的 main   action  ，字符串以逗号隔开
 * @property string $path 下载相对地址
 * @property string $file_name 文件名
 * @property int $status 0:待处理 1:执行完成
 * @property int $is_delete 是否删除
 * @property int $type 1:hris导出
 * @property string $created_at 创建时间
 * @property string $finish_at 完成时间
 * @property string $args_json
 */
class HcmExcelTask extends \yii\db\ActiveRecord
{

    const TASK_STATUS_OVER = 1;//执行完成
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'hcm_excel_task';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('backyard');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['status', 'is_delete', 'type'], 'integer'],
            [['created_at', 'finish_at'], 'safe'],
            [['args_json'], 'string'],
            [['staff_info_id'], 'string', 'max' => 30],
            [['executable_path'], 'string', 'max' => 200],
            [['action_name', 'path', 'file_name'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'staff_info_id' => 'Staff Info ID',
            'executable_path' => 'Executable Path',
            'action_name' => 'Action Name',
            'path' => 'Path',
            'file_name' => 'File Name',
            'status' => 'Status',
            'is_delete' => 'Is Delete',
            'type' => 'Type',
            'created_at' => 'Created At',
            'finish_at' => 'Finish At',
            'args_json' => 'Args Json',
        ];
    }
}
