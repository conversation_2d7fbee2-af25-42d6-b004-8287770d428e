<?php

namespace app\models;

use Yii;
use Graze\GuzzleHttp\JsonRpc\Client;

class OssHelper
{
    public static function uploadFile($path, $lang = 'zh-CN',$bucket='WORK_ORDER',$java_source = false)
    {
        if (env('break_away_from_ms')) {
            return self::uploadFileHcm($path, $lang, $bucket, $java_source);
        }

        try {
            $filename = basename($path);
            //调用fle提供的OSS接口
            $client = Client::factory(Yii::$app->params['fle_rpc_endpoint'].'/com.flashexpress.fle.svc.api.OssSvc');
            $data = [
                $bucket,
                $filename,
                ''
            ];
            $body = array_merge([['locale' => $lang]], $data);
            $return = $client->send($client->request(time(), 'buildPutObjectUrl', $body));
            $upload = $return->getRpcResult();
            if($java_source){
                return  $upload;
            }
            $client = new \GuzzleHttp\Client();
            $response = $client->request('PUT',$upload['put_url'],[
                'headers'=> [
                    'Content-Type' => $upload['content_type'],
                    'Content-Disposition' => 'attachment; filename='.$filename,

                ],
                'body' => file_get_contents($path)
            ]);
            if ($response->getStatusCode() == 200){
                unlink($path);
            }

            $result =  [
                'file_name' => $filename,
                'bucket_name' => $upload['bucket_name'],
                'object_key' =>$upload['object_key'],
                'object_url' => $upload['object_url'],
            ];
            Yii::$app->logger->write_log(['uploadFileResult'=>$result],'info');
            return $result;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('OssHelper -> uploadFile，可能出现的问题：' . $e->getMessage().';行号：'.$e->getLine());
            return [
                'file_name' => '',
                'bucket_name' => '',
                'object_key' => '',
                'object_url' => '',
            ];
        }
    }

    public static function uploadFileHcm($path, $lang = 'zh-CN', $bucket = 'WORK_ORDER', $java_source = false)
    {
        $filename = basename($path);
        //调用fle提供的OSS接口
        $client = Client::factory(Yii::$app->params['hcm_rpc']);
        $data[] = [
            'biz_type' => $bucket,
            'filename' => $filename,
        ];
        $body   = array_merge([['local' => $lang]], $data);

        $return = $client->send($client->request(time(), 'buildPutObjectUrl', $body));
        $upload = $return->getRpcResult();

        if ($java_source) {
            return $upload;
        }
        $client   = new \GuzzleHttp\Client();
        $response = $client->request('PUT', $upload['put_url'], [
            'headers' => [
                'Content-Type'        => $upload['content_type'],
                'Content-Disposition' => 'attachment; filename=' . $filename,

            ],
            'body'    => file_get_contents($path),
        ]);
        if ($response->getStatusCode() == 200) {
            unlink($path);
        }

        return [
            'file_name'   => $filename,
            'bucket_name' => $upload['bucket_name'],
            'object_key'  => $upload['object_key'],
            'object_url'  => $upload['object_url'],
        ];
    }

}