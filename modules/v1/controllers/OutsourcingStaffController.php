<?php

namespace app\modules\v1\controllers;

use app\controllers\RestFulController;
use app\libs\BusinessException;
use app\services\base\OutsourcingStaffService;
use Exception;
use Yii;

//外协员工
class OutsourcingStaffController extends RestFulController
{
    //外协员工批量上传离职
    public function actionBatchUploadLeaveJob() {
        $params = Yii::$app->request->post();
        try {
            $params['user_id']   = $this->fbid;
            $params['language'] = Yii::$app->language;

            $check_result = OutsourcingStaffService::getInstance()->checkUndoneLeaveJobTask($params);
            if($check_result) {
                return $this->err($this->lang->get('batch_outsourcing_leave_job_error_9'));
            }

            if(!$_FILES) {
                return $this->err($this->lang->get('batch_outsourcing_leave_job_error_10'));
            }

            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            // 读取文件
            $excel_data = $excel->openFile($_FILES["file_excel"]["tmp_name"])
                ->openSheet()
                //->setSkipRows(1)
                ->setType([
                    \Vtiful\Kernel\Excel::TYPE_INT,
                    \Vtiful\Kernel\Excel::TYPE_STRING
                ])
                ->getSheetData();

            if($excel_data[0][0] != '*Outsource staff id' || $excel_data[0][1] != '*Resignation Date') {
                return $this->err($this->lang->get('batch_outsourcing_leave_job_error_14'));
            }

            if(count($excel_data) < 2) {
                return $this->err($this->lang->get('batch_outsourcing_leave_job_error_11'));
            }

            if(count($excel_data) > 2001) {
                return $this->err($this->lang->get('batch_outsourcing_leave_job_error_12'));
            }

            unset($excel_data[0]);
            $params['file_name'] = $_FILES["file_excel"]["name"];
            $params['excel_data'] = array_values($excel_data);

            $result = OutsourcingStaffService::getInstance()->addBatchUploadLeaveJobTask($params);
            if($result) {
                return $this->succ(['success']);
            }
            return $this->err('error');
        } catch (BusinessException $b_e) {
            return $this->err($b_e->getMessage());
        } catch (\Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'actionBatchUploadResign',
                'message'  => $e->getMessage(),
                'line'     => $e->getLine(),
                'file'     => $e->getFile(),
                'params'   => $params,
            ]);
            return $this->err('error');
        }
    }

    /**
     * 上传任务列表
     * @return array
     */
    public function actionLeaveJobTaskList()
    {
        $params = Yii::$app->request->get();
        try {
            $params['user'] = $this->user;
            $data           = OutsourcingStaffService::getInstance()->getLeaveJobTaskList($params);
            return $this->succ($data);
        } catch (Exception $e) {
            Yii::$app->logger->write_log([
                'function' => 'actionLeaveJobTaskList',
                'message'  => $e->getMessage(),
                'line'     => $e->getLine(),
                'file'     => $e->getFile(),
                'params'   => $params,
            ]);
            return $this->err('error');
        }
    }

    //获取上传文件/是否有权限 batch_outsourcing_leave_job_edit
    public function actionRequestList()
    {
        $params['user_id'] = $this->user['id'];
        $data              = OutsourcingStaffService::getInstance()->getRequestList($params);
        return $this->succ(['data' => $data]);
    }
}