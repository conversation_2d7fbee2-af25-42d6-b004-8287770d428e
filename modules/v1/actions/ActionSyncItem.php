<?php

namespace app\modules\v1\actions;

use app\modules\v1\business\Staff;
use Yii;

class ActionSyncItem extends \yii\base\Action
{
    // 身份证号
    // 手机号码
    // 银行卡号
    public function run()
    {
        $staffInfoId = $this->controller->fbid;
        $post = Yii::$app->request->post();
        $attrs = [];
        foreach (['identity', 'mobile', 'bank_no', 'personal_email','bank_type','nick_name'] as $attr) {
            if (!empty($post[$attr])) {
                $attrs[$attr] = $post[$attr];
            }
        }

        if (isset($post['personal_email']) && !$post['personal_email']) {
            $attrs['personal_email'] = '';
        }

        if(isset($post['nick_name']) && !$post['nick_name']) {
            $attrs['nick_name'] = '';
        }

        if(isset($post['identity']) && !empty($post['identity'])) {
            if(YII_COUNTRY == 'TH') {
                if(!preg_match("/(^[A-Za-z0-9]{8,13}$)|(^[A-Za-z0-9]{18}$)/u", $post['identity'])) {
                    return $this->controller->err('The identity is invalid');
                }
            }elseif(YII_COUNTRY == 'ID'){
                if(!preg_match("/(^[A-Za-z0-9_\-]{8,16}$)/u", $post['identity'])) {
                    return ['identity',' id_staff_identity_length_error'];
                }
            }elseif(YII_COUNTRY == 'PH') {
                if(!preg_match("/(^[A-Za-z0-9_\-]{8,30}$)/u", $post['identity'])) {
                    return ['identity','PH_staff_identity_length_error_2'];
                }
            }elseif(YII_COUNTRY == 'LA') {
                if(!preg_match("/(^[A-Za-z0-9_\-]{8,15}$)/u", $post['identity'])) {
                    return ['identity','LA_staff_identity_length_error_2'];
                }
            } else {
                if(!preg_match("/(^[A-Za-z0-9_\-]{1,30}$)/u", $post['identity'])) {
                    return ['identity','staff_identity_length_error_2'];
                }
            }
        }

        if (!empty($attrs['mobile'])) {
            if (strtoupper(YII_COUNTRY) == 'PH') {
                // 菲律宾
                $attrs['mobile'] = str_pad($attrs['mobile'], 11, "0", STR_PAD_LEFT);
            } else if (strtoupper(YII_COUNTRY) == 'LA' || strtoupper(YII_COUNTRY) == 'MY') {
                // 老挝
                if (strpos( $attrs['mobile'],'0') !== 0) {
                    $pad_len = strlen($attrs['mobile']) == 9 ? 10 : 11;
                    $attrs['mobile'] = str_pad($attrs['mobile'], $pad_len, "0", STR_PAD_LEFT);
                }
            }
        }

        if (empty($attrs)) {
            return $this->controller->err(['miss params']);
        }

        if (($res = Staff::updateItems($staffInfoId, $attrs, $staffInfoId)) && $res !== true) {
            return $this->controller->err($res);
        }
        Yii::$app->logger->write_log('ActionSyscItem修改工号信息,工号:'.$staffInfoId.';工号信息:'. json_encode($attrs),'info');
        return $this->controller->succ(['staff_info_id' => (int) $staffInfoId]);
    }

}
