<?php

namespace app\modules\v1\business;

use app\libs\Enums\Enums;
use app\libs\Enums\JobTransferEnums;
use app\libs\Enums\StoreCategoryEnums;
use app\libs\Enums\JobTitleEnums;
use app\libs\Enums\SuspendReasonEnums;
use app\libs\Enums\SysDepartmentEnums;
use app\libs\RedisListKeyEnums;
use app\libs\RocketMQ;
use app\libs\ValidationException;
use app\models\backyard\Banklist;
use app\models\backyard\HrJobDepartmentRelation;
use app\models\backyard\HrJobGradeChange;
use app\models\backyard\HrStaffAnnexInfo;
use app\models\backyard\HrStaffContract;
use app\models\backyard\HrStaffInfoExtend;
use app\models\backyard\LeaveManager;
use app\models\backyard\OsStaffInfoExtend;
use app\models\backyard\SettingEnv;
use app\models\backyard\StaffCriminalRecord;
use app\models\backyard\SysStoreType;
use app\models\backyard\VehicleInfo;
use app\models\backyard\SysStore as BySysStore;
use app\models\fle\StaffInfo as StaffInfo_fle;
use app\models\fle\StaffInfoPosition;
use app\models\fle\StoreDeliveryBarangayStaffInfo;
use app\models\fle\StoreRemittanceBill;
use app\models\fle\StoreRemittanceRecord;
use app\models\fle\SysDepartment;
use app\models\fle\SysDistrict;
use app\models\fle\SysStore;
use app\models\fle\SysStore as SysStore_fle;
use app\models\manage\BaseStaffInfo;
use app\models\manage\HrAreaManagerStore;
use app\models\manage\HrEmails;
use app\models\manage\HrJobTitle;
use app\models\manage\HrJobTitleRole;
use app\models\manage\HrOperateLogs;
use app\models\manage\HrStaffInfoPosition;
use app\models\manage\HrStaffItems;
use app\models\manage\HrStaffManageRegions;
use app\models\manage\HrStaffSalary;
use app\models\manage\HrStaffTransferLog;
use app\models\manage\StaffAttachment;
use app\models\manage\StaffInfo;
use app\models\manage\StaffItems;
use app\models\manage\SysManagePiece;
use app\models\manage\SysManageRegion;
use app\models\manage\SysStoreTemp;
use app\models\OssHelper;
use app\services\base\AfterEntryService;
use app\services\base\HrStaffAnnexInfoService;
use app\services\base\LeaveManageService;
use app\services\base\OutSourcingOrderService;
use app\services\base\OutSourcingService;
use app\services\base\ResumeService;
use app\services\base\SettingEnvService;
use app\services\base\StaffFpsService;
use app\services\base\StaffLeaveReasonService;
use app\services\base\StaffRelationsService;
use app\services\base\StaffService;
use app\services\base\StaffShiftService;
use app\services\base\StaffSupportService;
use app\services\base\StaffSyncService;
use app\services\base\SysConfigService;
use app\services\base\VehicleInfoService;
use app\services\base\CompanyService;
use DateTime;
use MQ\Model\TopicMessage;
use MQ\MQClient;
use Yii;
use yii\base\InvalidConfigException;
use yii\db\Exception;
use yii\db\Query;


class Staff extends \yii\base\BaseObject
{


    public static $frontLinePosition = [101,98,97,16,451,37,110,1015,13,452,272,572,271,661,220,675,753,473,745,992,540,664,545,546,924,158,917,300,918,1000,801];//一线职位

    // 可查看全部数据
    public static $view_all_roles = ['PERSONNEL_MANAGER', 'HRIS_MANAGER', 'SUPER_ADMIN', 'HR_SYSTEMADMIN', 'SHARED_SERVICES', 'SYSTEM_ADMIN'];
    // 可查看管辖范围内的数据
    public static $view_manage_range_roles = ['HRBP', 'HR_SERVICE', 'ER', 'PAYROLL_MANAGER', 'TALENT_ACQUISITION', 'HRD', 'PERSONNEL_COMMISSIONER', 'HR_OFFICER', 'TALENT_ACQUISITION_NW'];
    // 编辑、查看按钮管辖范围内的数据
    public static $ev_manage_range_roles = ['HRBP', 'HR_SERVICE', 'ER'];
    // 查看按钮管辖范围内的数据
    public static $v_manage_range_roles = ['PAYROLL_MANAGER', 'TALENT_ACQUISITION', 'HRD', 'PERSONNEL_COMMISSIONER', 'HR_OFFICER', 'TALENT_ACQUISITION_NW'];

    public static $change_leader_list = [];

    public static $branch_supervisor_job_title_id = 16;//网点主管id

    //updateItems方法是否记录员工操作记录
    public static $update_items_is_save_operate_log = true;

    //updateItems方法记录员工操作记录Type
    public static $update_items_operate_type = Enums::OPERATE_LOG_TYPE_STAFF;
    //Save方法是否更新职级
    public static $save_is_update_job_grade = true;

    //离职、停职变为在职(非待离职) 或 待离职到非待离职(在职) 处理hold
    public static $handle_hold = true;

    /**
     * 角色权限
     * @param $staffIds
     * @param $fbid
     * @return array
     * @throws \yii\base\InvalidConfigException
     *
     */
    public static function rolesPermission($staffIds, $fbid)
    {
        $result = ['view_staff_ids' => $staffIds, 'edit_staff_ids' => []];
// 所有员工
        if (count(array_intersect(array_diff(self::$view_all_roles, ['SHARED_SERVICES', 'SYSTEM_ADMIN']), Yii::$app->user->identity->getPermission()['fbi.role']))) {
            // 所有员工
            $result['edit_staff_ids'] = $staffIds;
            return $result;
        }
        if (count(array_intersect(self::$ev_manage_range_roles,
            Yii::$app->user->identity->getPermission()['fbi.role']))) {
            // 查看 编辑 管辖范围权限
            $domin = self::dominDepartmentsAndStores($fbid);
            Yii::$app->logger->write_log('rolesPermission 结果：' . json_encode(['fbi' => $fbid, 'domin' => $domin], JSON_UNESCAPED_UNICODE), 'info');
//            $orConditions[] = ['IN', 'hr_staff_info.node_department_id', $domin['flash_home_departments'] ?? []];
            $orConditions[] = ['AND',
                ['hr_staff_info.sys_store_id' => '-1'],
                ['IN', 'hr_staff_info.node_department_id', $domin['flash_home_departments']],
            ];

            if($domin['stores']){
                if (in_array('-2', $domin['stores'])) {
                    // 权限所有网点， 取除了总部的所有网点
                    $orConditions[] = ['NOT IN', 'hr_staff_info.sys_store_id', ['-1']];
                } else {
                    $orConditions[] = ['IN', 'hr_staff_info.sys_store_id', $domin['stores'] ?? 0];
                }
            }
            $query = (new Query())->select('hr_staff_info.*')->from('hr_staff_info');
            $query = $query->Where(['IN', 'hr_staff_info.staff_info_id', $staffIds]);
            $orConditions = array_merge(['OR'], $orConditions);
            $query->andWhere($orConditions);
            $rows = $query->all(Yii::$app->get('r_backyard'));
            $result['edit_staff_ids'] = array_column($rows, 'staff_info_id');
        }
        return $result;
    }
    /**
     * 获取根据职位变动职级自动调整的一线职位
     * @return false|int[]|string[]
     */
    public static function getFortLinePosition(){
        $frontLinePosition = SettingEnv::find()->where(['code' => 'auto_update_job_grade_position'])->one();
        if(!empty($frontLinePosition) && $frontLinePosition->set_val){
            return explode(',', $frontLinePosition->set_val);
        }
        //马来 1199
        //菲律宾 1194
        return self::$frontLinePosition;
    }




    public static function getIdentity($staffId, $identity = false)
    {
        if ($identity !== false) {
            $identity = preg_replace('/[^0-9]/i', '', $identity);
            $identity = (int) $identity;
            if (!empty($identity)) {
                return (string) $identity;
            }
        }

        $len = 13 - strlen($staffId);
        return str_repeat('0', $len) . $staffId;
    }

    public static function combination($form)
    {
        if (empty($form['staff_info_id'])) {
            $model = new StaffInfo();
        } else {
            $model = StaffInfo::find()->where(['staff_info_id' => $form['staff_info_id']])->one();
            $_vehicle_source  = $model->vehicle_source;
            $_vehicle_use_date  = $model->vehicle_use_date;
        }

        // 员工回复在职逻辑处理
        //self::reinstatement($model,$form);

        if(isset($form['hire_date']) && !empty($form['hire_date'])) {
            $form['hire_date'] = date('Y-m-d 00:00:00', strtotime($form['hire_date']));
        }
        if(isset($form['leave_date']) && !empty($form['leave_date'])) {
            $form['leave_date'] =  date('Y-m-d 00:00:00', strtotime($form['leave_date']));
        }
        if(isset($form['stop_duties_date']) && !empty($form['stop_duties_date'])) {
            $form['stop_duties_date'] = date('Y-m-d 00:00:00', strtotime($form['stop_duties_date']));
        }
        //20211 泰国 企业号码不允许编辑
        if (isCountry('TH') && !empty($form['mobile_company'])) {
            unset($form['mobile_company']);
        }

        // 这load  会把表单参数直接绑定到model存在的字段
        if (!$model || !$model->load($form, '')) {
            Yii::$app->logger->write_log('combination model load，可能出现的原因'.json_encode($model->getErrors(), JSON_UNESCAPED_UNICODE));
            return 'exception';
        }
        //不验证
        $model->superJobTransfer =  0;
        $model->superJobTransferNotJump = 1;
        //是否为单个处理员工信息。
        $model->is_single = $form['is_single'] ?? false;
        // 直线主管
        $model->directManager = $form['manager'] ?? null;
        // 虚拟主管
        $model->indirectManager = $form['indirect_manager'] ?? null;


        if ($model->isOfficialPartner()) {
            $model->newCarType = $form['staff_car_type'];
        } else {
            // 车辆类型 不能修改
            if (!empty($form['staff_car_type']) && empty($model->getItems('CAR_TYPE'))) {
                $model->newCarType = $form['staff_car_type'];
            }
        }
        //项目期数
        if (!empty($form['project_num'])) {
            $model->projectNum = $form['project_num'];
        }
        // 车牌号 不能修改
        if(!empty($form['staff_car_no']) && empty($model->getItems('CAR_NO'))){
            $model->newCarNo = $form['staff_car_no'];
        }
        //车辆来源 不能修改
        if(!empty($form['vehicle_source'])){
            $model->vehicle_source = empty($_vehicle_source) ? $form['vehicle_source']:$_vehicle_source;
        }
        //用车开始时间 不能修改
        if(!empty($form['vehicle_use_date'])){
            $model->vehicle_use_date = empty($_vehicle_use_date) ? $form['vehicle_use_date']:$_vehicle_use_date;
        }

        //TODO 马来 才有的车类型 不能修改
        if(!empty($form['vehicle_type_category']) && empty($model->getItems('VEHICLE_TYPE_CATEGORY'))){
            $model->vehicleTypeCategory = $form['vehicle_type_category'] ;
        }

        // 外协 结算
        $model->newPayType = $form['pay_type'] ?? null;

        $model->supervisorMobile = $form['supervisor_mobile'] ?? null;
        if (YII_COUNTRY == 'ID' && !empty($form['staff_info_id'])) {
            if (isset($form['supervisor_mobile']) && strlen($form['supervisor_mobile']) != 13 && strpos($form['supervisor_mobile'], "0") !== 0) {
                $model->supervisorMobile = str_pad($form['supervisor_mobile'], strlen($form['supervisor_mobile']) + 1, "0", STR_PAD_LEFT);
            }
        }

        $model->profileObjectKey = $form['profile_object_key'] ?? null;
        $model->manageAreaName = $form['manage_area_name'] ?? [];
        $model->driver_license = $form['driver_license'] ?? null;//驾驶证号
        $model->outsourcing_type = $form['outsourcing_type'] ?? null;//外协类型
        // 主账号
        $model->newMasterStaff = $form['master_staff'] ?? null;
        $model->is_sub_staff = !empty($model->newMasterStaff) ? 1 : 0;
        $model->bankNoName = $form['bank_no_name'] ?? null;
        //if (empty($form['bank_no_name'])) {
        //    $model->bankNoName = $model->name;
        //}
        if(empty($form['staff_type']) && $form['formal'] == 0) {
            $model->staff_type = 1;
            $form['hire_type'] =  $form['hire_type'] ?? BaseStaffInfo::HIRE_OS_TYPE_COMMON;//11 是普通外协
        }
        $model->equipment_cost = $form['equipment_cost'] ?? null;
        $model->equipment_deduction_type = $form['equipment_deduction_type'] ?? null;
        $model->img_driving_license = $form['img_driving_license'] ?? null;
        $model->img_identity = $form['img_identity'] ?? null;
        $model->img_driver_license = $form['img_driver_license'] ?? null;
        $model->img_bank_info = $form['img_bank_info'] ?? null;
        $model->img_household_registry = $form['img_household_registry'] ?? null;
        $model->img_temp_contract = $form['img_temp_contract'] ?? null;
        $model->img_vehicle = $form['img_vehicle'] ?? null;
        $model->patment_markup_other = $form['patment_markup_other'] ?? null;
        $model->payment_markup = $form['payment_markup'] ?? '';
        $model->manageRegion = $form['region_id'] ?? null;
        $model->managePiece  = $form['piece_id'] ?? '';
        $is_have_disability_certificate = $form['is_have_disability_certificate'] ?? 0;
        if($is_have_disability_certificate == 0) {
            $model->disability_certificate = '';
            $model->disability_certificate_front = '';
            $model->disability_certificate_reverse = '';
        } else {
            $model->disability_certificate_front = $form['disability_certificate_front'] ?? '';
            $model->disability_certificate_reverse = $form['disability_certificate_reverse'] ?? '';
        }


        if(!empty($form['stop_payment_type_arr'])) {
            $model->stop_payment_type = implode(",",$form['stop_payment_type_arr']);
        } else {
            $model->stop_payment_type = '';
        }

        $form['position_category'] = $form['position_category'] ?? [];
        if (!is_array($form['position_category'])) {
            return ['role', 'exception'];
        }

        //指定工号角色增加超短角色
        if(!empty($form['staff_info_id'])){
            $super_admin_position_staff = SettingEnv::find()->where(['code' => 'super_admin_position_staff'])->one();
            if(!empty($super_admin_position_staff) && $super_admin_position_staff->set_val){
                $super_admin_position_staffs = explode(',',$super_admin_position_staff->set_val);
                if(in_array($form['staff_info_id'],$super_admin_position_staffs)){
                    $form['position_category'] = array_merge($form['position_category']??[],[99]);
                }
            }
        }

        $model->positionCategory = $form['position_category'] ?? [];
        // 身份证正反面
        $model->identityFrontKey = $form['identity_front_key'] ?? null;
        $model->identityReverseKey = $form['identity_reverse_key'] ?? null;
        if ($model->isOfficialPartner()) {
            $model->scenario = StaffInfo::SCENARIO_OFFICAL_PARTNER;
        }

        $model->graduate_school = $form['graduate_school'] ?? null;
        $model->major = $form['major'] ?? null;

        $form_graduate_time = $form['graduate_time'] ?? null;
        if(!empty($form_graduate_time)) {
            $form_graduate_time = strtotime($form_graduate_time) === false ? null : $form_graduate_time;
        }
        $model->graduate_time = $form_graduate_time;
        $model->education_old = $form['education'] ?? null;
        $model->nationality_old = $form['nationality'] ?? null;

        $form_birthday = $form['birthday'] ?? null;
        if(!empty($form_birthday)) {
            $form_birthday = strtotime($form_birthday) === false ? null : $form_birthday;
        }
        $model->birthday = $form_birthday;
        $model->register_country = $form['register_country'] ?? null;
        $model->register_province = $form['register_province'] ?? null;
        $model->register_city = $form['register_city'] ?? null;
        $model->register_district = $form['register_district'] ?? null;
        $model->register_postcodes = $form['register_postcodes'] ?? null;
        $model->register_house_num = $form['register_house_num'] ?? null;
        $model->register_village_num = $form['register_village_num'] ?? null;
        $model->register_village = $form['register_village'] ?? null;
        $model->register_alley = $form['register_alley'] ?? null;
        $model->register_street = $form['register_street'] ?? null;
        $model->residence_country = $form['residence_country'] ?? null;
        $model->residence_province = $form['residence_province'] ?? null;
        $model->residence_city = $form['residence_city'] ?? null;
        $model->residence_district = $form['residence_district'] ?? null;
        $model->residence_postcodes = $form['residence_postcodes'] ?? null;
        $model->residence_house_num = $form['residence_house_num'] ?? null;
        $model->residence_village_num = $form['residence_village_num'] ?? null;
        $model->residence_village = $form['residence_village'] ?? null;
        $model->residence_alley = $form['residence_alley'] ?? null;
        $model->residence_street = $form['residence_street'] ?? null;
        $model->dad_first_name = $form['dad_first_name'] ?? null;
        $model->dad_last_name = $form['dad_last_name'] ?? null;
        $model->mum_first_name = $form['mum_first_name'] ?? null;
        $model->mum_last_name = $form['mum_last_name'] ?? null;
        $model->relatives_relationship = $form['relatives_relationship'] ?? null;//紧急联系人关系
        $model->relatives_first_name = $form['relatives_first_name'] ?? null;
        $model->relatives_last_name = $form['relatives_last_name'] ?? null;
        $model->relatives_call_name = $form['relatives_call_name'] ?? null;//紧急联系人称呼
        $model->relatives_mobile = $form['relatives_mobile'] ?? null;//紧急联系人电话
        $model->residential_address = $form['residential_address'] ?? null;//居住地址详情

        if (YII_COUNTRY == 'ID' && !empty($form['staff_info_id'])) {
            if (isset($form['relatives_mobile']) && strlen($form['relatives_mobile']) != 13 && strpos($form['relatives_mobile'], "0") !== 0) {
                $model->relatives_mobile = str_pad($form['relatives_mobile'], strlen($form['relatives_mobile']) + 1, "0", STR_PAD_LEFT);
            }
        }

        $model->working_country_old = $form['working_country'] ?? null;
        $model->new_shift_id = $form['shift_id'] ?? null;
        $model->register_detail_address = $form['register_detail_address'] ?? null;//马来新增地址详情字段
        $model->residence_detail_address = $form['residence_detail_address'] ?? null;//马来新增地址详情字段
        $model->race = $form['race'] ?? null;//种族
        $model->religion = $form['religion'] ?? null;//宗教
	    $model->staff_province_code = (isset($form['sys_store_id']) && isset($form['staff_province_code']) && $form['sys_store_id'] == '-1') ? $form['staff_province_code'] : null;//工作所在洲 只有马来 网点 为-1 的时候  有值
        $model->bank_branch_name = $form['bank_branch_name'] ?? null;//银行分行名称
        $model->household_registration = $form['household_registration'] ?? null;//户籍照号
        $model->ptkp_state = $form['ptkp_state'] ?? null;//PTKP状态
        $model->tax_card = $form['tax_card'] ?? null;//税卡号
        $model->backup_bank_no = $form['backup_bank_no'] ?? null;       // 备用卡卡号
        $model->backup_bank_no_name = $form['backup_bank_no_name'] ?? null;//备用卡持卡人信息
        $model->backup_bank_type = $form['backup_bank_type'] ?? null;// 备用卡类型
        $model->default_rest_day_date = $form['default_rest_day_date'] ?? [];// 轮休默认休息日
        $model->fund_num = $form['fund_num'] ?? null;                           // 公积金号
        $model->social_security_num = $form['social_security_num'] ?? null;     // 社保号
        $model->medical_insurance_num = $form['medical_insurance_num'] ?? null; // 医疗保险号

        if (YII_COUNTRY == 'ID') {
            $model->residence_rt = $form['residence_rt'] ?? null;       //居住地邻组
            $model->residence_rw = $form['residence_rw'] ?? null;       // 居住地居委会
            $model->register_rt  = $form['register_rt'] ?? null;        //户口所在地邻组
            $model->register_rw  = $form['register_rw'] ?? null;        // 户口所在地居委会
        }

        if (YII_COUNTRY == 'TH'){
            $model->social_security_leave_date  = $form['social_security_leave_date'] ?? null;        // 社保离职日期
        }

        //type扩展，原有1：公司；2：公司下的部门；3：是组织下的部门，新增扩展 4：boss级别（coo/cto/c..o）；5:gropu ceo 级别
        if(in_array($model->formal,[1, 4])) {
            $model->sys_department_id = (string)SysGroupDeptV2::SearchSysDeptId($model->node_department_id);
            if (!StaffService::getInstance()->isLnt($model->contract_company_id)) {
                $model->contract_company_id = CompanyService::getInstance()->getStaffContractCompany($form['staff_info_id'] ?? 0,
                    $model->node_department_id,false);
            }
        }


        if ($model->state == BaseStaffInfo::STATE_ON_JOB && !empty($form['wait_leave_date'])) {
            $model->leave_date = $form['wait_leave_date'];
            $model->wait_leave_state = 1;
        }
//
//        - PH系统中，历史数据所有11位数以下的手机号需要在前方补0，补齐为11位保存
//
//        - LA系统中，如果输入10位数手机号
//        - 如果第一位不是0，需要在前方补0，补齐为11位保存
//        - 如果第一位是0，不补0
//
//        - MY系统中，如果输入10位数手机号
//        - 如果第一位不是0，需要在前方补0，补齐为11位
//        - 如果第一位是0，不补0

//        - 2021.10. https://l8bx01gcjr.feishu.cn/docs/doccn942Ax5xP7JQo1Z8OOIp1xd 文档规则修改如下
//        - LA和MY系统中，
//        - 如果第一位是0，不补0,
//        - 如果手机号是9位，补齐为10位，如果是10位则补齐为11位
//        -

        if (!empty($form['mobile'])) {
            if (YII_COUNTRY == 'PH') {
                // 菲律宾
                $model->mobile = str_pad($form['mobile'], 11, "0", STR_PAD_LEFT);
            } elseif (YII_COUNTRY == 'ID' && !empty($form['staff_info_id'])) {
                if (strlen($form['mobile']) != 13 && strpos($form['mobile'], "0") !== 0 ) {
                    $model->mobile = str_pad($form['mobile'], strlen($form['mobile'])+1, "0", STR_PAD_LEFT);
                }
            } else if (YII_COUNTRY == 'LA' || YII_COUNTRY == 'MY') {
                // 老挝
                if (strpos( $form['mobile'],'0') !== 0) {
                    $pad_len = strlen($form['mobile']) == 9 ? 10 : 11;
                    $model->mobile = str_pad($form['mobile'], $pad_len, "0", STR_PAD_LEFT);
                }
            }
        }
        if (!empty($form['mobile_company'])) {
            if (YII_COUNTRY == 'PH') {
                // 菲律宾
                $model->mobile_company = str_pad($form['mobile_company'], 11, "0", STR_PAD_LEFT);
            } elseif (YII_COUNTRY == 'ID' && !empty($form['staff_info_id'])) {
                if (strlen($form['mobile_company']) != 13 && strpos($form['mobile_company'], "0") !== 0 ) {
                    $model->mobile_company = str_pad($form['mobile_company'], strlen($form['mobile_company'])+1, "0", STR_PAD_LEFT);
                }
            } else if (YII_COUNTRY == 'LA' || YII_COUNTRY == 'MY') {
                // 老挝
                if (strpos( $form['mobile_company'],'0') !== 0) {
                    $pad_len = strlen($form['mobile']) == 9 ? 10 : 11;
                    $model->mobile_company = str_pad($form['mobile_company'], $pad_len, "0", STR_PAD_LEFT);
                }
            }
        }

        if(isset($form['hire_type'])){
            $model->hire_type = $form['hire_type'] ?? null;
            //雇佣类型是13  出现签约日期
            if($model->hire_type == StaffInfo::HIRE_TYPE_UN_PAID && !empty($form['signing_date'])){
                $model->signing_date = $form['signing_date'];
            }
        }

        //工作天数&轮休规则
        //week_working_day 每周工作天数
        //rest_type 轮休规则
        if(isset($form['working_day_rest_type']) && !empty($form['working_day_rest_type'])) {
            $working_day_rest_type_arr = str_split($form['working_day_rest_type']);
            $model->week_working_day = $working_day_rest_type_arr[0] ?? 0;
            $model->rest_type = $working_day_rest_type_arr[1] ?? 0;
        } else {
            $model->week_working_day = 0;
            $model->rest_type = 0;
        }

        if (YII_COUNTRY == 'TH' && !empty($form['social_security_leave_date']) && $model->state == BaseStaffInfo::STATE_RESIGN){
            $model->social_security_leave_date = $form['social_security_leave_date'] ?? '';
        }

        //除 hub 外协公司，员工的外协公司枚举id
        if (YII_COUNTRY == 'TH' && $model->formal == 0){
            $model->company_item_id = !empty($form['company_item_id']) ? $form['company_item_id'] : 0;
            $model->company_name_ef = OutSourcingService::getInstance()->getOsCompanyEnums($model->company_item_id);
        }

        //外协是否完善地址信息
        if (YII_COUNTRY == 'TH' && $model->formal == 0)
        {
            $model->is_complete_address = 0;
            if( !empty($model->register_country)
        && !empty($model->register_province)
        && !empty($model->register_city)
        && !empty($model->register_district)
        && !empty($model->register_postcodes)

        && (!empty($model->register_house_num)
        || !empty($model->register_village_num)
        || !empty($model->register_village)
        || !empty($model->register_alley)
        || !empty($model->register_street))
            ) {
                $model->is_complete_address = OsStaffInfoExtend::IS_COMPLETE_ADDRESS_YES;
            }
        }

        unset($model->creater);
        unset($model->created_at);
        return $model;
    }

    //验证提交字段
    public static function validate($form)
    {
        if (!empty($form['driver_license'])) {
            if(!preg_match("/^[\x{0E00}-\x{0E7F}a-zA-Z0-9\/\-.]{1,20}$/u", $form['driver_license'])) {
                return ['excel_driver_license','outsourcing_tip4'];
            }
        }

        // 身份证 验证
        if(!empty($form['identity'])) {
            if(YII_COUNTRY == 'TH') {
                // 泰国
                if(!preg_match("/(^[A-Za-z0-9]{8,13}$)|(^[A-Za-z0-9]{18}$)/u", $form['identity'])) {
                    return ['identity','staff_identity_length_error_1'];
                }

                if($form['formal'] == BaseStaffInfo::FORMAL_OUTSOURCE && isset($form['company_item_id']) && $form['company_item_id'] == OutSourcingService::COMPANY_ITEM_PREMIER && !preg_match(OutSourcingService::PREG_MATCH_OS_COMPANY_PREMIER, $form['identity'])){
                    return ['identity','os_staff_identity_length_premier'];
                }

            } elseif (YII_COUNTRY == 'PH') {
                // 菲律宾
                if(!preg_match("/([A-Za-z0-9\-_]{8,30}$)/u", $form['identity'])) {
                    return ['identity','PH_staff_identity_length_error_2'];
                }
            } elseif (YII_COUNTRY == 'MY' || YII_COUNTRY == 'VN') {
                // 马来西亚 // 越南
                if(!preg_match("/(^[A-Za-z0-9]{8,13}$)/u", $form['identity'])) {
                    return ['identity','staff_identity_length_error_2'];
                }
            } elseif (YII_COUNTRY == 'LA') {
                // 老挝
                if(!preg_match("/([A-Za-z0-9\-_]{8,15}$)/u", $form['identity'])) {
                    return ['identity','LA_staff_identity_length_error_2'];
                }
            }elseif (YII_COUNTRY == 'ID') {
                // 老挝
                if(!preg_match("/(^[A-Za-z0-9\-]{8,16}$)/u", $form['identity'])) {
                    return ['identity','id_staff_identity_length_error'];
                }
            }
        }

        if(!empty($form['email'])) {
            if (!filter_var($form['email'],FILTER_VALIDATE_EMAIL)) {
                return ['email', 'common_exception'];
            }
            if(!checkdnsrr(substr($form['email'],strpos($form['email'],'@')+1))){
                return ['email','common_exception'];
            }
        }

        if(!empty($form['personal_email'])) {
            if (!filter_var($form['personal_email'],FILTER_VALIDATE_EMAIL)) {
                return ['personal_email','common_exception'];
            }
            if (!checkdnsrr(substr($form['personal_email'], strpos($form['personal_email'], '@') + 1))) {
                return ['personal_email', 'common_exception'];
            }
        }

        // 个人号码
        if (!empty($form['mobile'])) {
            if(strtoupper(YII_COUNTRY) == 'ID') {
                if (!preg_match('/(^[0-9]{8,13}$)/', $form['mobile'])) {

                    return ['mobile','mobile_exception_2'];
                }
            }else{
                if (!preg_match('/(^[0-9]{10,11}$)/', $form['mobile'])) {

                    return ['mobile','mobile_exception'];
                }
            }

        }

        if (!empty($form['mobile_company'])) {
            if(strtoupper(YII_COUNTRY) == 'ID') {
                if (!preg_match('/(^[0-9]{8,13}$)/', $form['mobile_company'])) {

                    return ['mobile_company','mobile_exception_2'];
                }
            }else{
                if (!preg_match('/(^[0-9]{10,11}$)/', $form['mobile_company'])) {

                    return ['mobile_company','mobile_exception'];
                }
            }

        }
        if(!empty($form['bank_no']) && !empty($form['bank_type'])) {
            if(($res = StaffInfoCheck::validateBankNoStingLen($form['bank_no'], $form['bank_type'])) && $res !== true) {
                return $res;
            }
        }

        if (YII_COUNTRY == 'PH' && !empty($form['backup_bank_no']) && !empty($form['backup_bank_type'])) {   // 菲律宾需要验证备用银行卡信息
            if(($res = StaffInfoCheck::validateBankNoStingLen($form['backup_bank_no'], $form['backup_bank_type'],'backup_bank_no')) && $res !== true) {
                return $res;
            }
        }

        // 菲律宾 税号 非必填，匹配9位数字
        if (YII_COUNTRY == 'PH' && !empty($form['tax_card'])) {
            if (!preg_match('/(^[0-9]{9}$)/', $form['tax_card'])) {
                return ['tax_card','The tin card number is incorrect, please enter 9 digits'];
            }
        }
        //高级主管不能配置管辖全部网点
        if (isCountry('TH') && !empty($form['job_title']) && $form['job_title'] == JobTitleEnums::TH_SENIOR_BRANCH_SUPERVISOR) {
            if (!empty($form['stores'])) {
                $stores = is_json($form['stores']) ? json_decode($form['stores'], true) : $form['stores'];
                if (in_array(StaffRelationsService::$all_id, $stores)) {
                    return ['senior_branch_supervisor', 'disable_manage_all_store'];
                }
            }
        }
        return true;
    }

    //保存员工信息
    public static function save(StaffInfo $model, $fbid, $reset_password = false, $black_origin_enum = [], $job_grade_change_source = 3)
    {
        //判断如果银行类型为2 并且卡号为空
        if(YII_COUNTRY == 'TH' && !$model->isNewRecord && $model->bank_type == 2 && empty($model->bank_no)) {
            $info = StaffInfo::find()->select(['bank_type','bank_no'])->where(['staff_info_id' => $model->staff_info_id])->asArray()->one();
            if(count($info) > 0) {
                if($info['bank_type'] == 1) {
                    $model->bank_type = $info['bank_type'];
                    $model->bank_no = $info['bank_no'];
                }
            }
        }

        if(!empty($model->bank_no) && !empty($model->bank_type)) {
            if(($res = StaffInfoCheck::validateBankNoStingLen($model->bank_no, $model->bank_type)) && $res !== true) {
                return $res;
            }
        }

        $default_manger = 0;
        $default_nationality = 0;
        $default_working_country = 0;
        $default_education =  0;

        $baseStaff = BaseStaffInfo::find()->where(['staff_info_id' => $model->staff_info_id])->one();
        if($baseStaff) {
            $default_manger =$baseStaff->manger;
            $default_nationality = $baseStaff->nationality;
            $default_working_country = $baseStaff->working_country ;
            $default_education =  $baseStaff->education;
            if($baseStaff->state != 3 && $model->state == 3) {
                $baseStaff->stop_duties_count++;
                $model->stop_duties_count = $baseStaff->stop_duties_count;
            }
            $current_hire_type = $baseStaff->hire_type;
        }

        //直线上级不能是自己 工号17245 17008
        if(!in_array($model->staff_info_id , [17245, 17008])) {
            if(!$model->isNewRecord && $model->staff_info_id == $model->directManager) {
                return ['supervisor_cannot_himself'];
            }
        }

        // 雇佣类型为 13 不能 变更为 其他雇佣类型
        if(isset($current_hire_type) &&  in_array($current_hire_type,StaffInfo::$agentTypeTogether) && !in_array($model->hire_type,StaffInfo::$agentTypeTogether)) {
            return ['hire_type', 'hire_type_error_13'];
        }

        if($model->hire_type == StaffInfo::HIRE_TYPE_PART_TIME_AGENT && $model->week_working_day != StaffInfo::WEEK_WORKING_DAY_9){
            return ['working_day_rest_type_error_2'];
        }

        // 雇佣类型 不为 或 13 不能 变更为 其他雇佣类型
        if(isset($current_hire_type) &&  !in_array($current_hire_type,StaffInfo::$agentTypeTogether) &&  in_array($model->hire_type,StaffInfo::$agentTypeTogether) ) {
            return ['hire_type', 'hire_type_error_13'];
        }

        if($model->formal == 1 && $model->is_sub_staff == 0 && !in_array($model->hire_type, [1, 2, 3, 4, StaffInfo::HIRE_TYPE_UN_PAID,StaffInfo::HIRE_TYPE_PART_TIME_AGENT])) {
            return ['hire_type', 'hire_type_error_1'];
        }
        if($model->formal == 4 && $model->hire_type != 5) {
            return ['hire_type', 'hire_type_error_1'];
        }

        if(in_array($model->formal, [1, 4]) && $model->staff_type != 0) {
            $model->staff_type = 0;
        }
        //名字过滤特殊字符
        $model->name = nameSpecialCharsReplace($model->name);
        //外协验证身份证是否在黑名单
        if($model->formal == 0 && $model->state != 2) {
            $validate_out_sourcing_identity = OutSourcingService::getInstance()->validateOutSourcingIdentity($model->identity,
                $model->hire_type, $black_origin_enum);
            if($validate_out_sourcing_identity !== true) {
                return $validate_out_sourcing_identity;
            }
            $car_type_country =  OutSourcingOrderService::getInstance()->getJobTitleCarListFromCache();
            //外协 车辆类型改为按职位自动赋值
            $model->newCarType =  $car_type_country[$model->job_title] ?? null;

        }
        //一个网点只能有一个网点主管 职位
        if (StaffService::getInstance()->checkStoreJobTitleIsOverstaffed($model)) {
            return ['store_job_title_limit'];
        }

        if(in_array($model->state, [BaseStaffInfo::STATE_RESIGN, BaseStaffInfo::STATE_SUSPENSION]) || ($model->formal == BaseStaffInfo::FORMAL_OUTSOURCE && empty($model->leave_date))) {
            $model->wait_leave_state = BaseStaffInfo::WAIT_LEAVE_STATE_NO;//如果是离职 停职状态 待离职状态强制转变成0
        }
        //高级主管不能配置管辖全部网点
        if (StaffRelationsService::getInstance()->disableJobTitleConfigAllStore($model)) {
            return ['disable_manage_all_store'];
        }

        $model->manger = !empty($model->directManager) ? (int)$model->directManager : $default_manger;
        $model->nationality = !empty($model->nationality_old) ? (int)$model->nationality_old : $default_nationality;
        $model->working_country = !empty($model->working_country_old) ? (int)$model->working_country_old : $default_working_country;
        $model->education = !empty($model->education_old) ? (int)$model->education_old : $default_education;

        //外协、正式、实习生
        if(self::$save_is_update_job_grade &&
            in_array($model->formal, [0, 1, 4]) &&
            (
                $model->isAttributeChanged('node_department_id') ||
                $model->isAttributeChanged('job_title')
            )) {
            // 变更 职级
            //1. 如果是非一线职位：职级不随职位自动变更
            //此处是对一线的处理
            if (in_array($model->job_title, Staff::getFortLinePosition())) {
                $model->job_title_grade_v2 = self::getDefaultJobTitleGrade($model->node_department_id, $model->job_title);
            }
        }

        $isNewRecord = $model->isNewRecord;

        //个人代理的新入职员工 签约日期等于入职日期
        if ($isNewRecord &&  in_array($model->hire_type , StaffInfo::$agentTypeTogether)) {
            $model->signing_date = mb_substr($model->hire_date,0,10);
        }

        // 新入职员工的职级生效日期为入职日期
        if ($isNewRecord) {
            $model->job_grade_effective_date = mb_substr($model->hire_date,0,10);
        } else if ($baseStaff->job_title_grade_v2 != $model->job_title_grade_v2) {
            $model->job_grade_effective_date = gmdate('Y-m-d',time() + TIME_ADD_HOUR * 3600);
        }

        //是否需要修改 班次信息
        $isSaveShift = ($model->isAttributeChanged('node_department_id') || $model->isAttributeChanged('job_title') ||  $model->isAttributeChanged('sys_store_id')) ? true : false;

        $manager_store_list = [];
        if(in_array(YII_COUNTRY, ['TH', 'PH', 'MY']) && $model->state == 2) {
            $manager_store_list = SysStore::find()->where(['manager_id' => $model->staff_info_id])->andWhere(['state' => 1])->andWhere(['category' => StaffService::getInstance()->change_store_manager_store_category])->asArray()->all();
        }

        if(($model->isAttributeChanged('state') && $model->state == 2) || ($model->isAttributeChanged('wait_leave_state') && $model->wait_leave_state == 1)) {
            $model->leave_source = 9;
            $model->is_history_leave = 1;
        }

        //恢复在职后 清空日期和离职日期
        if($model->state == BaseStaffInfo::STATE_ON_JOB && $model->wait_leave_state == 0){
            $model->leave_date = null;
            $model->stop_duties_date = null;
            $model->stop_duty_reason = null;
            $model->leave_source = 0;
            $model->leave_type = null;
            $model->leave_reason = null;
        }

        $oldState = $model->getOldAttribute('state');
        $before_position_category = $model->getPositionCategory();
        $after_position_category = $model->positionCategory;
        $trans = $model->getDb()->beginTransaction();

        $changeAnnexInfo = [
            'identity'              => $model->identity,                // 身份证
            'bank_no'               => $model->bank_no,                // 银行卡
            'fund_num'              => $model->fund_num,                // 公积金
            'medical_insurance_num' => $model->medical_insurance_num,   // 医疗保险
            'social_security_num'   => $model->social_security_num,     // 社保
            'TAX_CARD'              => $model->tax_card,                // 税卡
            'BACKUP_BANK_NO'        => $model->backup_bank_no,          // 备用银行卡
            'source'                => 'save'
        ];

        if (in_array($model->formal,[1,4]) && $model->is_sub_staff == 0 && isCountry('PH') && !$isNewRecord &&
            (
                empty($model->email)
                || $model->sys_store_id != $baseStaff->sys_store_id
                || $model->job_title != $baseStaff->job_title
                || (array_diff($before_position_category, $after_position_category) || array_diff($after_position_category, $before_position_category))
            )
        ) {
            $checkParams['staff_info_id'] = $model->staff_info_id;
            $checkParams['sys_store_id']  = $model->sys_store_id;
            $checkParams['job_title']     = $model->job_title;
            $checkParams['role_id']       = $after_position_category;
            $checkResult                  = StaffService::getInstance()->checkCompanyEmail($checkParams);
            Yii::$app->logger->write_log(['auto_create_email_save' => ['params' => $checkParams, 'res' => $checkResult]], 'info');

            if (!empty($checkResult['email'])) {
                //写入库，发送邮箱时使用。
                $autoCreateEmail['staff_info_id'] = $model->staff_info_id;
                $autoCreateEmail['date'] = gmdate('Y-m-d', time()+TIME_ADD_HOUR*3600);
                $autoCreateEmail['email'] = $checkResult['email'];
                StaffService::getInstance()->autoCreateEmail($autoCreateEmail);

                $messageBody = [
                    'staff_info_id' => $model->staff_info_id,
                    'email'         => $checkResult['email'],
                ];

                $rmq                       = new RocketMQ('update-staff-info');
                $sendData['jsonCondition'] = json_encode($messageBody, JSON_UNESCAPED_UNICODE);
                $sendData['handleType']    = RocketMQ::TAG_HR_STAFF_UPDATE;
                $rmq->setShardingKey($model->staff_info_id);
                $result = $rmq->sendOrderlyMsg($sendData, 5);

                Yii::$app->logger->write_log([$messageBody, $result], 'info');
            }
        }

        if (!$model->isNewRecord && $model->isAttributeChanged('state') && $model->state == 2) {
            $changeAttributes = [
                'state' => $model->getOldAttribute('state'),
                'payment_state' => $model->getOldAttribute('payment_state'),
                'payment_markup' => $model->getOldAttribute('payment_markup'),
                'stop_payment_type' => $model->getOldAttribute('stop_payment_type'),
                'wait_leave_state' => $model->getOldAttribute('wait_leave_state'),
                'is_auto_system_change' => 0,
                'leave_source' => $model->getOldAttribute('leave_source'),
                'is_history_leave' => $model->getOldAttribute('is_history_leave'),
            ];

//            $changeAttributes = [
//                'state' => $model->state,
//                'payment_state' => $model->payment_state,
//                'payment_markup' => $model->payment_markup,
//                'stop_payment_type' => $model->stop_payment_type,
//                'wait_leave_state' => $model->wait_leave_state,
//                'is_auto_system_change' => 0,
//                'leave_source' => $model->leave_source,
//                'is_history_leave' => $model->is_history_leave,
//            ];

            // 主账号离职，子账号也必须都是离职状态
//            if (!$model->is_sub_staff) {
//                foreach ($model->subStaffs as $staff) {
//                    if ($staff->state == 1) {
//                        return ['master_account', 'set_master_leave_tip'];
//                    }
//                }
//            }

            // 该员工是片区负责人，不能离职 修改后可以变成离职
            //if (SysDistrict::find()->where(['courier_id' => $model->staff_info_id, 'deleted' => 0])->exists()) {
            //    return ['state', 'state_deny_change_district'];
            //}

            $attributes = array_filter($model->getAttributes(), function ($k) {
                return in_array($k, ['state', 'leave_date', 'leave_reason', 'leave_type','hire_date', 'bank_no', 'payment_state', 'payment_markup','stop_payment_type','wait_leave_state','is_auto_system_change','leave_source','is_history_leave']);
            }, ARRAY_FILTER_USE_KEY);
            if (!$model->updateAttributes($attributes)) {
                Yii::$app->logger->write_log('staff save update state error' . json_encode($model->getErrors()),'info');
                $trans->rollback();
                return ['common_exception'];
            }
            $model->afterSave(false, $changeAttributes);
            $model->refresh();

            //离职工号发送mq, 判断是否是同步工号，是同步工号,则不发it工单
            $res = StaffService::getInstance()->getSyncStaffInfo($model->staff_info_id);
            if(!$res) {
                self::sendStaffLeavelMQ(['staff_info_id' => $model->staff_info_id, 'leave_date' => $model->leave_date]);
            }

	        //发送离职邮件多国家离职信息同步
	        //https://l8bx01gcjr.feishu.cn/docs/doccnPlJmiRSig08LWcKFGVkdzo#
	        self::ResignationNoticeEmail($model->staff_info_id);


        } else {
            if ($model->isNewRecord) {

                $current = new \DateTime(date('Y-m-d'), new \DateTimeZone('+0000'));
                $hireDate = new \DateTime($model->hire_date, new \DateTimeZone('+0000'));
                $diff = $current->diff($hireDate);

                if ($diff->invert == 1 && $diff->days >= 4) {
                    $model->hire_date_origin = $model->hire_date;
                }
                if (empty($model->identity) && $model->isInFormal()) {
                    $model->identity = self::getIdentity($model->staff_info_id);
                }

                if($fbid == '-1' || !method_exists(Yii::$app,'getUser')) {
                    $model->creater = !empty($fbid) ? $fbid : 0;
                } else {
                    $model->creater = \Yii::$app->getUser()->identity['id'] ?? 0;
                }

                //$model->working_country = 1;//7244 原系统在职员工、新入职员工的工作所在国家默认泰国，可修改
            }

            // 是否是子账号
            if ($model->newMasterStaff) {
                $model->is_sub_staff = 1;
            }
            // 保存
            if (!$model->save()) {
                $trans->rollback();
                foreach ($model->getErrors() as $key => $row) {
                    return [$key, $row[0] ?? 'common_exception'];
                }
                return ['common_exception'];
            }
        }

        //外协协议注入
        if (
            YII_COUNTRY == 'MY'
            && $model->formal == BaseStaffInfo::FORMAL_OUTSOURCE
            && $model->hire_type == BaseStaffInfo::HIRE_OS_TYPE_COMMON
            && $model->outsourcing_type == BaseStaffInfo::OUTSOURCING_TYPE_INDIVIDUAL
            && $model->staff_info_id
        ) {
            Yii::$app->jrpc->sendArgeementSignPushToStaff([
                'staff_info_id' => $model->staff_info_id,
                'operator_id'   => $model->creater,
            ]);
        }

        //角色配置
        if(self::setPositionCategory($model->staff_info_id,$model->positionCategory) === false) {
            $trans->rollback();
            Yii::$app->logger->write_log('setPositionCategory staff_info_id: ' . $model->staff_info_id, 'info');

            return ['Configuration Role Error'];
        }

        if (!$isNewRecord && $baseStaff->job_title_grade_v2 != $model->job_title_grade_v2 && in_array($model->formal,[1,4])) {

            if (self::setJobGradeChangeLog($model->staff_info_id, $fbid, $baseStaff, $model, $job_grade_change_source) === false) {
                $trans->rollback();
                Yii::$app->logger->write_log('setJobGradeChangeLog fail staff_info_id: ' . $model->staff_info_id, 'info');
                return ['Save Job Grade Error'];
            }
        }


        if($isNewRecord && YII_COUNTRY != 'MY') {
            if($model->sys_store_id != '-1') {
                if(!empty($model->new_shift_id)) {
                    $job_name = Yii::$app->sysconfig->jobTitle[$model->job_title] ?? '';
                    StaffShift::create_new_staff_shift($model->staff_info_id, $model->new_shift_id, $fbid, ['staff_name' => $model->name, 'job_title' => $job_name,'hire_type'=>$model->hire_type]);//网点员工设置默认班次，win hr 设置的
                }
            } else {
                $job_name = Yii::$app->sysconfig->jobTitle[$model->job_title] ?? '';
                //主要针对 菲律宾 获取是否弹性打卡班次 id
                $shift_id = StaffShiftService::getInstance()->getShiftIdIsFlexible([
                    'node_department_id' => $model->node_department_id,
                    'job_title_id'       => $model->job_title,
                    'sys_store_id'       => $model->sys_store_id,
                    'shift_id'           => 7,
                ]);

                StaffShift::create_new_staff_shift($model->staff_info_id, $shift_id, $fbid, ['staff_name' => $model->name, 'job_title' => $job_name,'hire_type'=>$model->hire_type]);//所属网点为 Head office的员工入职时，自动设置班次为 09:00-18:00
            }
        }

        // 离职变为在职 需要关闭未回复的邮箱类 IT工单
        if  (2 == $oldState && 1 == $model->state) {
            StaffService::reinstatement($oldState, $model);
        }

        // 发送消息
        $msgBody = $model->getAttributes();
        $msgBody['operator_id'] = $fbid;
        $msgBody['staff_car_type'] = $model->newCarType ?? null;
        $msgBody['pay_type'] = $model->newPayType ?? null;
        $msgBody['manage_area_name'] = $model->manageAreaName;
        $msgBody['profile_object_key'] = $model->profileObjectKey;
        $msgBody['position_category'] = $model->positionCategory;
        $msgBody['is_sub_staff'] = $model->is_sub_staff ?? 0;
        $msgBody['master_staff'] = $model->newMasterStaff ?? 0;
        $msgBody['reset_password'] = $reset_password;//是否需要重置密码
        $msgBody['manager'] = $model->directManager;
        $msgBody['bank_no_name'] = $model->bankNoName ?? '';
        if($model->formal == 0) {
            $msgBody['outsourcing_category'] = $model->outsourcing_type == 'individual' ? 1 : 2;//外协员工类型
        } else {
            $msgBody['outsourcing_category'] = 0;//非外协传0
        }

        if (!StaffSyncService::getInstance()->saveStaffInfo($msgBody, $model->staff_info_id ? true : false)) {
            $trans->rollback();
            return ['msg send error'];
        }

        //Fulfillment Department 部门员工数据同步flash hr
        $dept_detail = SysDepartment::find()
            ->select(['id', 'ancestry', 'ancestry_v2', 'name', 'manager_id', 'manager_name', 'company_id'])
            ->where(['deleted' => 0])
            ->andWhere(['id' => $model->node_department_id])
            ->asArray()
            ->one();
        if ($isNewRecord) {
            //20001-Flash Fullfillment、30001-Flash Money、500001-F Commerce、60001-Flash Pay
            if(YII_COUNTRY == 'TH' && !empty($dept_detail) && in_array($dept_detail['company_id'], [ '70001'])) {
                //- 当员工HCM所属部门为：E-Commerce SAAS Sales[865]及下级：
                $e_commerce_ids = SysGroupDeptV2::getMyDeptList(865);
                if(!in_array($model->node_department_id, $e_commerce_ids)) {
                    $store_detail = SysStore::find()->where(['id' => $model->sys_store_id])->asArray()->one();
                    $store_name = !empty($store_detail) ? $store_detail['name'] : '';
                    $msgBody['company_id'] = $dept_detail['company_id'];
                    $msgBody['birthday'] = $model->birthday ?? '';
                    $msgBody['nationality'] = $model->nationality ?? '';
                    $msgBody['sys_store_name'] = $store_name;
                    Yii::$app->hris_to_flash_hr_mns->syncStaff($msgBody);
                }

            }
        }

        if(in_array($model->formal,[1,4])){
            // 同步更新附件表数据和状态
            $modifyStaffAnnexRet = HrStaffAnnexInfoService::getInstance()->modifyHrStaffAnnexInfo($model->staff_info_id, $changeAnnexInfo);
            if (!$modifyStaffAnnexRet) {
                $trans->rollback();
                return 'modifyHrStaffInfoAnnexInfo fail';
            }
        }

        //外协的，增加员工拓展信息：外协公司枚举值，是否完善信息
        if ($model->formal == BaseStaffInfo::FORMAL_OUTSOURCE) {
            $osData['staff_info_id']       = $model->staff_info_id;
            $osData['company_item_id']     = empty($model->company_item_id) ? 0 : $model->company_item_id;
            $osData['is_complete_address'] = empty($model->is_complete_address) ? 0 : $model->is_complete_address;
            $addOsRes                      = OutSourcingService::getInstance()->addOsStaffInfoExtend($osData);
            if (!$addOsRes) {
                $trans->rollback();
                return ['os extends info error'];
            }
        }

        $trans->commit();
        //新增员工 加年假额度记录初始化
        $mq = new RocketMQ('annual-leave-days');
        $mq->sendToMsg(['staff_info_id' => $model->staff_info_id, 'lang' => 'en'],3);
        Yii::$app->logger->write_log('annual-leave-days ' . $model->staff_info_id, 'info');
        $today = gmdate('Y-m-d', time() + TIME_ADD_HOUR * 3600);
        //合同到期日变更 续签了，释放hold
        if (isCountry('MY') && !empty($model->contract_expiry_date)
            && !empty($baseStaff->contract_expiry_date)
            && strtotime($baseStaff->contract_expiry_date) != strtotime($model->contract_expiry_date)
            && strtotime($model->contract_expiry_date) >=  strtotime($today .' + 7 days')
            && $model->state != BaseStaffInfo::STATE_RESIGN
        ) {
            $mq = new RocketMQ('renew-contract-release-hold');
            $mq->sendToMsg([
                'staff_info_id' => $model->staff_info_id,
                'before_date'   => $baseStaff->contract_expiry_date,
                'after_date'    => $model->contract_expiry_date,
                'state'         => $model->state,
            ], 3);
        }
        if (
            !$isNewRecord &&
            $model->formal == BaseStaffInfo::FORMAL_YES &&
            $model->is_sub_staff == BaseStaffInfo::IS_SUB_STAFF_NO &&
            substr($model->hire_date,0,10) != substr($baseStaff->hire_date,0,10) && 
            (
                (
                    isCountry('PH')
                    && $model->hire_type == BaseStaffInfo::HIRE_TYPE_MONTHLY_SALARY
                ) or
                (
                    isCountry('TH') &&
                    (
                        $model->hire_type == BaseStaffInfo::HIRE_TYPE_FORMAL || 
                        (in_array($model->hire_type,[BaseStaffInfo::HIRE_TYPE_DAILY_SALARY,BaseStaffInfo::HIRE_TYPE_HOURLY_WAGE]) && $model->hire_times >= 365) || 
                        ($model->hire_type == BaseStaffInfo::HIRE_TYPE_MONTHLY_SALARY && $model->hire_times >= 12)
                    )
                )
            )
        ) {
            $callbackParams = [
                'staff_info_id' => $model->staff_info_id,
                'hire_date'     => $model->hire_date,
            ];
            Yii::$app->redis->lpush(RedisListKeyEnums::CREATE_HR_PROBATION, json_encode($callbackParams));
        }

        if(in_array($model->state, [2, 3])) {
            //加黑名单 $model['state']=2
            //增加case  离职后修改离职原因也触发黑名单逻辑
            if ($model->state == BaseStaffInfo::STATE_RESIGN && $baseStaff->state != BaseStaffInfo::STATE_RESIGN){
                self::addStaffBlacklist($model->staff_info_id, $fbid,true);
            }elseif ($model->state == BaseStaffInfo::STATE_RESIGN && $baseStaff->state == BaseStaffInfo::STATE_RESIGN && $model->leave_reason != $baseStaff->leave_reason){
                self::addStaffBlacklist($model->staff_info_id, $fbid,false,$baseStaff->toArray());
            }

            //停职离职发送消息
            self::courierStaffLeavelsendMessage($model->staff_info_id);
            if(YII_COUNTRY == 'TH' && !empty($dept_detail) && in_array($dept_detail['company_id'], [ '70001'])) {
                //- 当员工HCM所属部门为：E-Commerce SAAS Sales[865]及下级：
                $e_commerce_ids = SysGroupDeptV2::getMyDeptList(865);
                if(!in_array($model->node_department_id, $e_commerce_ids)) {
                    $body = [
                        'staff_info_id' => $model->staff_info_id,
                        'state' => $model->state,
                        'leave_date' => $model->leave_date,
                        'stop_duties_date' => $model->stop_duties_date,
                        'company_id' => $dept_detail['company_id']
                    ];
                    Yii::$app->hris_to_flash_hr_mns->syncStaffState($body);
                }
            }
        }

        if(in_array(YII_COUNTRY, ['TH', 'PH', 'MY']) && $model->state == 2) {
            //菲律宾 马来 员工离职判断如果是组织负责人 更换直线上级
            (new StaffManager())->updateStaffLeaveManager($model->staff_info_id, $fbid, $manager_store_list);
        }

        if (in_array($model->formal,[1,4]) && $model->is_sub_staff == 0){
            //在职状态变更
            if ($baseStaff) {
                StaffService::getInstance()->staffStateChange($baseStaff->toArray(), $model->toArray(),$fbid);
            }

            // 同步更新winhr电子合同里面的入职日期
            Staff::updateContractWinHr($model->staff_info_id,$model->hire_date);

            //hrbp 角色变更 oa 人才盘点菜单变更
            if(in_array(68, $before_position_category) && !in_array(68, $after_position_category)) {
                //角色变更后 没有hrbp角色 删除oa 人才盘点菜单
                Yii::$app->jrpc->sync_update_hrbp_talent_review_permissions($model->staff_info_id, 0);
            }
            if(!in_array(68, $before_position_category) && in_array(68, $after_position_category)) {
                //角色变更后 新增hrbp 角色 增加oa 人才盘点菜单
                Yii::$app->jrpc->sync_update_hrbp_talent_review_permissions($model->staff_info_id, 1);
            }
            //hrbp、Hr Service角色表更
            StaffService::getInstance()->syncApprovalJurisdictionChanges($before_position_category, $after_position_category, $model->staff_info_id, $fbid);

            $before_working_day_rest_day = '';
            if($baseStaff) {
                $before_working_day_rest_day = $baseStaff->week_working_day . $baseStaff->rest_type;
            }
            $after_working_day_rest_day = $model->week_working_day . $model->rest_type;
            if($before_working_day_rest_day != $after_working_day_rest_day) {
                //通知轮休
                $params = [
                    'staff_info_id'         => $model->staff_info_id,
                    'date_at'               => gmdate('Y-m-d', time() + TIME_ADD_HOUR * 3600),
                    'remark'                => $before_working_day_rest_day.'->'.$after_working_day_rest_day,
                    'is_create'             => $isNewRecord,
                    'default_rest_day_date' => $model->default_rest_day_date,
                ];
                Yii::$app->jrpc->changeWorkingDayRestDay($params);
            }
            //更新排班建议
            if ($isNewRecord) {
                $storeIds = [$model->sys_store_id];
                Yii::$app->jrpc->updateSchedulingSuggestNumber(['store_ids' => $storeIds]);
            } else {
                if (
                    $baseStaff && (
                        $before_working_day_rest_day != $after_working_day_rest_day
                        || $baseStaff->state != $model->state
                        || $baseStaff->job_title != $model->job_title
                        || $baseStaff->sys_store_id != $model->sys_store_id
                    )
                ) {
                    $storeIds = $baseStaff->sys_store_id != $model->sys_store_id ? [
                        $baseStaff->sys_store_id,
                        $model->sys_store_id,
                    ] : [$model->sys_store_id];
                    Yii::$app->jrpc->updateSchedulingSuggestNumber(['store_ids' => $storeIds]);
                }
            }
            //员工在职状态变化
            if (!$isNewRecord && $baseStaff
                && in_array($baseStaff->formal,[BaseStaffInfo::FORMAL_YES,BaseStaffInfo::FORMAL_TRAINEE])
                && $baseStaff->is_sub_staff == BaseStaffInfo::WAIT_LEAVE_STATE_NO) {
                $onJobChangeBefore = [
                    'state' => $baseStaff->state,
                    'wait_leave_state' => $baseStaff->wait_leave_state,
                    'leave_date' => $baseStaff->leave_date,
                    'leave_source' => $baseStaff->leave_source,
                    'leave_type' => $baseStaff->leave_type,
                    'leave_reason' => $baseStaff->leave_reason,
                    'hire_date' => $baseStaff->hire_date,
                    'hire_type' => $baseStaff->hire_type,
                    'stop_duties_date' => $baseStaff->stop_duties_date,
                    'stop_duty_reason' => $baseStaff->stop_duty_reason,
                    'job_title_grade_v2' => $baseStaff->job_title_grade_v2,
                    'operate_id' => $fbid,
                ];
                $onJobChangeAfter = [
                    'state' => $model->state,
                    'wait_leave_state' =>  $model->wait_leave_state,
                    'leave_date' => $model->leave_date,
                    'leave_source' => $model->leave_source,
                    'leave_type' => $model->leave_type,
                    'leave_reason' => $model->leave_reason,
                    'hire_date' => $model->hire_date,
                    'hire_type' => $model->hire_type,
                    'stop_duties_date' => $model->stop_duties_date,
                    'stop_duty_reason' => $model->stop_duty_reason,
                    'job_title_grade_v2' => $model->job_title_grade_v2,
                    'operate_id' => $fbid,
                ];
                StaffSyncService::getInstance()->producerStaffOnJobChange($baseStaff->staff_info_id,$onJobChangeBefore,$onJobChangeAfter);
                //同步hcm 计算员工short notice
                if (isCountry('MY') && !empty($model->leave_date)) {
                    $mq = new RocketMQ('cal-short-notice');
                    $mq->sendToMsg(['staff_info_id' => $baseStaff->staff_info_id],2);
                }
            }

            // 变更离职log表逻辑
            if (
                $model->is_sub_staff == BaseStaffInfo::IS_SUB_STAFF_NO &&
                $baseStaff &&
                $model->state == BaseStaffInfo::STATE_RESIGN &&
                $baseStaff->state == BaseStaffInfo::STATE_RESIGN &&
                (
                    (isset($model->leave_reason) && $model->leave_reason != $baseStaff->leave_reason) ||
                    (isset($model->leave_date) && date('Y-m-d', strtotime($model->leave_date)) != date('Y-m-d', strtotime($baseStaff->leave_date)))
                )
            ){
                LeaveManageService::getInstance()->updateLeaveManageLog([
                    'staff_info_id'              => $model->staff_info_id,
                    'leave_date'                 => $model->leave_date ?? $baseStaff->leave_date,
                    'leave_reason'               => $model->leave_reason ?? $baseStaff->leave_reason,
                    'leave_type'                 => $model->leave_type ?? $baseStaff->leave_type,
                    'leave_source'               => $model->leave_source ?? $baseStaff->leave_source,
                    'operate_id'                 => $fbid,
                ]);
            }

            if($baseStaff && (($model->state == 2 && $baseStaff->state != 2) || ($baseStaff->wait_leave_state != 1 && $model->wait_leave_state == 1)) && $model->is_sub_staff == 0) {

                //同步leave_manager
                $leave_date = date('Y-m-d', strtotime($model->leave_date));
                LeaveManageService::getInstance()->updateLeaveManage([
                    'staff_info_id'           => $model->staff_info_id,
                    'leave_date'              => $leave_date,
                    'leave_source'            => $model->leave_source,
                    'leave_type'              => $model->leave_type,
                    'leave_reason'            => $model->leave_reason,
                    'operate_id'              => $fbid,
                    'state'                   => $model->state,
                    'before_wait_leave_state' => $baseStaff->wait_leave_state,
                    'before_state'            => $baseStaff->state,
                ]);
                //同步新离职资产 员工离职信息
                StaffService::getInstance()->pushRedisSyncResignAssetsStaff([
                    'event_type' => 'staff_resign',
                    'params'     => [
                        'staff_info_id'      => $model->staff_info_id,
                        'source'             => $model->leave_source,
                        'operation_staff_id' => $fbid,
                        'staff_state'        => $model->state,
                        'wait_leave_state'   => $model->wait_leave_state,
                        'leave_date'         => $leave_date,
                        'last_work_date'     => date('Y-m-d', strtotime($leave_date) - 86400), //最后工作日，离职前一天
                    ],
                ]);
            }

            //添加停职记录
            if($baseStaff && $model->state == BaseStaffInfo::STATE_SUSPENSION && $model->is_sub_staff == 0) {

                $origin_info['state'] = $baseStaff->state;
                $origin_info['wait_leave_state'] = $baseStaff->wait_leave_state;
                $origin_info['leave_date'] = $baseStaff->leave_date;

                $suspensionData = [
                    'staff_info_id'    => $model->staff_info_id,
                    'stop_duties_date' => $model->stop_duties_date,
                    'stop_duty_reason' => $model->stop_duty_reason,
                    'operate_id'       => $fbid,
                    'origin_info'       => json_encode($origin_info, JSON_UNESCAPED_UNICODE)
                ];
                if($baseStaff->state != BaseStaffInfo::STATE_SUSPENSION) {
                    StaffService::getInstance()->addSuspensionManageLog($suspensionData);
                } else {
                    StaffService::getInstance()->updateSuspensionManageLog($suspensionData);
                }
            }

            Yii::$app->logger->write_log('removeBlackGreyList start staff_info_id: ' . $model->staff_info_id, 'info');
            if ($baseStaff && in_array($model->state,[BaseStaffInfo::STATE_ON_JOB,BaseStaffInfo::STATE_SUSPENSION]) && $baseStaff->state == BaseStaffInfo::STATE_RESIGN){
                // 作废 连续旷工三天以上 的黑灰名单
                StaffService::getInstance()->removeBlackGreyList($baseStaff->toArray(),$fbid,$model->toArray());
            }

            //离职变为在职 或 待离职到非待离职
            if ($baseStaff
                && (
                    ($model->state == BaseStaffInfo::STATE_ON_JOB && $baseStaff->state == BaseStaffInfo::STATE_RESIGN)
                    ||
                    ($baseStaff->state == BaseStaffInfo::STATE_ON_JOB && $baseStaff->wait_leave_state == BaseStaffInfo::WAIT_LEAVE_STATE_YES &&
                        $model->state == BaseStaffInfo::STATE_ON_JOB && $model->wait_leave_state == BaseStaffInfo::WAIT_LEAVE_STATE_NO)
                    ||
                    ($baseStaff->state == BaseStaffInfo::STATE_SUSPENSION && $model->state == BaseStaffInfo::STATE_ON_JOB && $model->wait_leave_state == BaseStaffInfo::WAIT_LEAVE_STATE_NO)
                )
                && $model->is_sub_staff == 0) {
                //原来是离职 或者原来是待离职 变成在职 同步新离职资产 员工在职信息
                StaffService::getInstance()->pushRedisSyncResignAssetsStaff([
                    'event_type' => 'staff_work',
                    'params'     => [
                        'staff_info_id'      => $model->staff_info_id,
                        'staff_name'         => $model->name,
                        'operation_staff_id' => $fbid,
                    ],
                ]);
            }

            //离职、停职变为在职(非待离职) 或 待离职到非待离职(在职)
            if ($baseStaff
                && (
                    ($model->state == BaseStaffInfo::STATE_ON_JOB &&
                        $model->wait_leave_state == BaseStaffInfo::WAIT_LEAVE_STATE_NO &&
                        $baseStaff->state != BaseStaffInfo::STATE_ON_JOB
                    ) ||
                    ($baseStaff->state == BaseStaffInfo::STATE_ON_JOB && $baseStaff->wait_leave_state == BaseStaffInfo::WAIT_LEAVE_STATE_YES &&
                        $model->state == BaseStaffInfo::STATE_ON_JOB && $model->wait_leave_state == BaseStaffInfo::WAIT_LEAVE_STATE_NO)
                ) && $model->is_sub_staff == 0) {

                $params = [
                    'staff_info_id' => $model->staff_info_id,                 //员工id
                    'hold_reason'   => 'incomplete_resignation_procedures',   //hold原因(BY申请离职)
                    'hold_source'   => 0,                                     //来源->全部来源都要处理
                    'handle_hold'   => 1,                                     //处理hold
                    'operator_id'   => $fbid,                                 //操作人
                ];
                Yii::$app->jrpc->staff_release_hold($params);
            }

            //当员工管理修改员工将在职状态改为“在职”，自动将「hold员工管理」的hold来源是「BY申请离职」的hold原因为「离职手续不全」的工资hold和提成hold状态，变为“已处理”
            if ($baseStaff && (
                ($model->state == BaseStaffInfo::STATE_ON_JOB && $model->wait_leave_state == BaseStaffInfo::WAIT_LEAVE_STATE_NO && $baseStaff->state != BaseStaffInfo::STATE_ON_JOB) ||
                ($model->state == BaseStaffInfo::STATE_ON_JOB && $model->wait_leave_state == BaseStaffInfo::WAIT_LEAVE_STATE_NO && $baseStaff->state == BaseStaffInfo::STATE_ON_JOB && $baseStaff->wait_leave_state == BaseStaffInfo::WAIT_LEAVE_STATE_YES)
                )
            ) {
                StaffService::getInstance()->pushSyncBackyardSourceHoldHandle([
                    'staff_info_id' => $model->staff_info_id,
                    'language' => Yii::$app->language
                ]);
            }

            if ($baseStaff && $model->manger != $baseStaff->manger && $model->is_sub_staff == 0) {
                //直线上级变更
                StaffService::getInstance()->pushRedisSyncResignAssetsStaff([
                    'event_type' => 'change_manager',
                    'params'     => [
                        'staff_info_id'      => $model->staff_info_id,
                        'before_manager_id'  => $baseStaff->manger,
                        'after_manager_id'   => $model->manger,
                        'operation_staff_id' => $fbid,
                    ],
                ]);
            }

            //同步fps 角色信息 如果角色发生变动
            if((array_diff($before_position_category, $after_position_category) || array_diff($after_position_category, $before_position_category))) {
                $fps_mq_data = [
                    'user_id' => $model->staff_info_id,
                    'user_name' => $model->name,
                    'organization_id' => $model->node_department_id,
                    'state' => $model->state,
                    'current_dept_is_hub'=> 1,
                    'role_id' => $model->positionCategory,
                    'clear_role' => !empty($model->positionCategory) ? 0 : 1
                ];
                StaffFpsService::getInstance()->sendStaffFpsMQ($fps_mq_data);

            }


            if(!$isNewRecord && $model->is_sub_staff == 0 && $baseStaff->sys_store_id != $model->sys_store_id && $model->sys_store_id != '-1') {
                $params = [
                    'staff_info_id' => $model->staff_info_id,
                    'store_id' => $model->sys_store_id,
                ];
                StaffSupportService::getInstance()->pushUpdateStaffSupport($params);
            }

            //非新建员工 变更后职位是网点主管 并且 原网点和变更后网点不一致
            //非新建员工 变更前职位不等于变更后职位 并且 变更后职位是网点主管
            //非新建员工 在职状态在职 并且 原在职状态是离职状态 并且职位是网点主管
            //新建员工 并且职位是网点主管

            $supervisorJobTitleId = StaffService::getInstance()->store_only_one_job_title;

            if((!$isNewRecord && in_array($model->job_title, $supervisorJobTitleId) && $baseStaff->sys_store_id != $model->sys_store_id)
                || (!$isNewRecord && $baseStaff->job_title != $model->job_title && in_array($model->job_title, $supervisorJobTitleId))
                || (!$isNewRecord && $model->state == BaseStaffInfo::STATE_ON_JOB && $baseStaff->state == BaseStaffInfo::STATE_RESIGN && in_array($model->job_title, $supervisorJobTitleId))
                || ($isNewRecord && in_array($model->job_title, $supervisorJobTitleId))) {
                //网点主管 变更网点 或者 职位变动成网点主管
                StaffService::getInstance()->pushChangeStoreSupervisorManager([
                    'store_id' => $model->sys_store_id,
                    'staff_info_id' => $model->staff_info_id,
                    'operator_id' => $fbid,
                    'manager_position_state' => in_array(YII_COUNTRY, ['TH', 'PH', 'MY']) ? BySysStore::MANAGER_POSITION_STATE_YES : null
                ]);
            }
        }
        // 不是新增  并且 修改了 部门职位 网点 需要修改班次信息
        if (!$isNewRecord && $isSaveShift) {
            //主要针对 菲律宾 修改弹性打卡班次
            StaffShiftService::getInstance()->saveStaffShift([
                'staff_info_id'      => $model->staff_info_id,
                'operater'           => $fbid,
                'node_department_id' => $model->node_department_id,
                'job_title_id'       => $model->job_title,
                'sys_store_id'       => $model->sys_store_id,
                'formal'             => $model->formal,
                'staff_type'         => $model->staff_type ?? 0,
            ]);
        }
        //员工变更部门信息回调oa
        if (!$isNewRecord) {
            $old_node_department_id = $baseStaff->node_department_id;
            if ($model->node_department_id != $old_node_department_id) {
                //cfo部门及子部门
                $cfo_department_ids = SysGroupDeptV2::getMyDeptList(333);
                if (in_array($old_node_department_id, $cfo_department_ids)) {
                    $api_param = [
                        'department_id' => $old_node_department_id,
                        'staff_info_id' => $model->staff_info_id,
                        'operator_id'   => $fbid,
                    ];
                    Yii::$app->logger->write_log('staff_info_financial: ' . json_encode($api_param), 'info');

                    Yii::$app->jrpc->sync_update_organization_cfo_department($api_param, 'en');
                }
            }
        }

//        self::syncStaffInfoFromStaffItems($model->staff_info_id);
        return true;
    }

    public static function syncStaffInfoFromStaffItems($staff_id)
    {
        $info =
        BaseStaffInfo::find()->where(['staff_info_id' => $staff_id])->asArray()->one();

        if ($info) {
            Yii::$app->logger->write_log('staff_info_item: ' . json_encode($info), 'info');
            $list = HrStaffItems::find()->select(['item', 'value'])->where(['staff_info_id' => $staff_id])->andWhere(['item' => ['MANGER', 'NATIONALITY', 'WORKING_COUNTRY']])->asArray()->all();
            $list = array_column($list, 'value', 'item');
            Yii::$app->logger->write_log('staff_info_item: ' . json_encode($list), 'info');
            $row = StaffInfo::getDb()->createCommand()->update('hr_staff_info', [
                'working_country' => $list['WORKING_COUNTRY'] ?? 0,
                'manger' => $list['MANGER'] ?? 0,
                'nationality' => $list['NATIONALITY'] ?? 0,
            ], 'staff_info_id='.$staff_id)->execute();
            Yii::$app->logger->write_log('staff_info_item: ' . $row, 'info');
        }

    }

    public static function view($staffInfoId = null)
    {
        $model = StaffInfo::find()->where(['staff_info_id' => $staffInfoId ?? -1])->one();//不能改成从库查询，有事务在调用

        if (!$model) {
            return [];
        }

        $info = $model->getAttributes();

        $items = $model->items;
        $info['bank_no_name'] = $items['BANK_NO_NAME'] ?? '';
        $info['bank_name'] = $items['BANK_NAME'] ?? 'TMB';
        $info['staff_car_type'] = $items['CAR_TYPE'] ?? null;
        $info['staff_car_no'] = $items['CAR_NO'] ?? null;
        $info['payment_state'] = $info['payment_state'] ?? 1;
        $info['oil_card_deposit']= $info['oil_card_deposit'] ?? 0;
        $info['driver_license']= $items['DRIVER_LICENSE'] ?? '';
        $info['outsourcing_type']  = $items['OUTSOURCING_TYPE'] ?? '';
        $info['equipment_cost']= $items['EQUIPMENT_COST'] ?? '';
        $equipment_deduction_type = $items['EQUIPMENT_DEDUCTION_TYPE'] ?? '';
        $info['equipment_deduction_type']  = $equipment_deduction_type;
        $info['equipment_deduction_type_text']  = $equipment_deduction_type ? Yii::$app->lang->get('equipment_deduction_' . $equipment_deduction_type) : '';
        //$info['stop_duty_reason_title'] = $info['stop_duty_reason'] ? Yii::$app->lang->get('stop_duty_reason_' . $info['stop_duty_reason']) : '';
        $stop_duty_reason = SuspendReasonEnums::SUSPEND_TYPE_REASON_MAP;
        $stop_duty_reason_key = $stop_duty_reason[$info['stop_duty_reason']] ?? '';
        $info['stop_duty_reason_title'] = Yii::$app->lang->get($stop_duty_reason_key);
        $info['patment_markup_other'] = $items['PAYMENT_MARKUP_OTHER'] ?? '';
        if($items['DISABILITY_CERTIFICATE_FRONT'] ?? false) {
            $info['disability_certificate_front'] = $items['DISABILITY_CERTIFICATE_FRONT'] ?? '';
            $info['disability_certificate_front_uri'] = Yii::$app->params['hris_profile_uri'] . $items['DISABILITY_CERTIFICATE_FRONT'] ?? '';
        }
        if($items['DISABILITY_CERTIFICATE_REVERSE'] ?? false) {
            $info['disability_certificate_reverse'] = $items['DISABILITY_CERTIFICATE_REVERSE'] ?? '';
            $info['disability_certificate_reverse_uri'] = Yii::$app->params['hris_profile_uri'] . $items['DISABILITY_CERTIFICATE_REVERSE'] ?? '';
        }

        if (YII_COUNTRY == "PH") {  // 菲律宾国家才会返回备用银行卡信息
            $backup_bank_type_list = Banklist::find()->select([
                'bank_id',
                'bank_name',
                'max_length',
                'min_length',
            ])->where(['bank_id' => 26])->one();
            $info['backup_bank_no'] = $items['BACKUP_BANK_NO'] ?? '';   // 备用银行卡卡号
            $info['backup_bank_no_name'] = $items['BACKUP_BANK_NO_NAME'] ?? $info['name'];// 备用银行卡持卡人信息
            $info['backup_bank_type'] = $items['BACKUP_BANK_TYPE'] ?? '';   // 备用银行卡类型
            $info['backup_bank_type_list'] = [
                [
                    'backup_bank_type' => 26,
                    'backup_bank_name' => Yii::$app->sysconfig->all_bank_type()['26'],
                    'max_length' => $backup_bank_type_list->max_length ?? 30,
                    'min_length' => $backup_bank_type_list->min_length ?? 1,
                ]
            ];
            // 之前写的bank_id 87 bank_list里并没有这个id 相对应的name是26 BDO 
            $info['tax_card'] = $items['TAX_CARD'] ?? '';   // 税号
        }

        $info['graduate_school']= $items['GRADUATE_SCHOOL'] ?? '';
        $info['major']= $items['MAJOR'] ?? '';

        $graduate_time = $items['GRADUATE_TIME'] ?? '';
        if(!empty($graduate_time)) {
            $graduate_time = strtotime($graduate_time) === false ? '' : $graduate_time;
        }
        $info['graduate_time']= $graduate_time;
        $info['education']= $items['EDUCATION'] ?? '';
        $info['nationality']= $info['nationality'] ? :($items['NATIONALITY'] ?? '');

        $birthday = $items['BIRTHDAY'] ?? '';
        if(!empty($birthday)) {
            $birthday = strtotime($birthday) === false ? '' : $birthday;
        }
        $info['birthday']= $birthday;
        $default_country_value = strval(SettingEnvService::getInstance()->getSetVal('s_f_d_nationality') ?: '1');
        //户口所在地、居住所在地 信息模块
        $info['register_country']= $items['REGISTER_COUNTRY'] ?? $default_country_value;
        $info['register_province']= $items['REGISTER_PROVINCE'] ?? '';
        $info['register_city']= $items['REGISTER_CITY'] ?? '';
        $info['register_district']= $items['REGISTER_DISTRICT'] ?? '';

        $info['residence_country']= $items['RESIDENCE_COUNTRY'] ?? $default_country_value;
        $info['residence_province']= $items['RESIDENCE_PROVINCE'] ?? '';
        $info['residence_city']= $items['RESIDENCE_CITY'] ?? '';
        $info['residence_district']= $items['RESIDENCE_DISTRICT'] ?? '';
        //居住地详情 my 外协在用
        $info['residential_address']= $items['RESIDENTIAL_ADDRESS'] ?? '';


        if($info['register_country'] == $default_country_value ){
            $info['register_province_name'] = SysConfigService::getInstance()->getProvinceInfoByCode($info['register_province'])['name']??'';
        }else{
            $info['register_province_name'] = $info['register_province'] ?? '';
        }

        if($info['residence_country'] == $default_country_value){
            $info['residence_province_name'] = SysConfigService::getInstance()->getProvinceInfoByCode($info['residence_province'])['name']??'';
        }else{
            $info['residence_province_name'] = $info['residence_province'] ?? '';
        }

        //马来只有市信息，且是输入框，所以直接展示市信息，不要乡字段
        if(YII_COUNTRY == 'MY'){
            $info['register_city_name'] = $info['register_city'] ?? '';
            $info['residence_city_name'] = $info['residence_city'] ?? '';
            $info['register_district_name'] = $info['register_district'] ?? '';
            $info['residence_district_name'] = $info['residence_district'] ?? '';
            $info['vehicle_type_category'] = $items['VEHICLE_TYPE_CATEGORY'] ?? '';//马来 车类型
        } else {
            if ($info['register_country'] == $default_country_value) {
                $info['register_city_name']     = SysConfigService::getInstance()->getCityInfoByCode($info['register_city'])['name'] ?? '';
                $info['register_district_name'] = SysConfigService::getInstance()->getDistrictInfoByCode($info['register_district'])['name'] ?? '';
            } else {
                $info['register_city_name']     = $info['register_city'] ?? '';
                $info['register_district_name'] = $info['register_district'] ?? '';
            }
            if ($info['residence_country'] == $default_country_value) {
                $info['residence_city_name']     = SysConfigService::getInstance()->getCityInfoByCode($info['residence_city'])['name'] ?? '';
                $info['residence_district_name'] = SysConfigService::getInstance()->getDistrictInfoByCode($info['residence_district'])['name'] ?? '';
            } else {
                $info['residence_city_name']     = $info['residence_city'] ?? '';
                $info['residence_district_name'] = $info['residence_district'] ?? '';
            }
        }

        $info['register_postcodes']= $items['REGISTER_POSTCODES'] ?? '';
        $info['register_house_num']= $items['REGISTER_HOUSE_NUM'] ?? '';
        $info['register_village_num']= $items['REGISTER_VILLAGE_NUM'] ?? '';
        $info['register_village']= $items['REGISTER_VILLAGE'] ?? '';
        $info['register_alley']= $items['REGISTER_ALLEY'] ?? '';
        $info['register_street']= $items['REGISTER_STREET'] ?? '';

        $info['residence_postcodes']= $items['RESIDENCE_POSTCODES'] ?? '';
        $info['residence_house_num']= $items['RESIDENCE_HOUSE_NUM'] ?? '';
        $info['residence_village_num']= $items['RESIDENCE_VILLAGE_NUM'] ?? '';
        $info['residence_village']= $items['RESIDENCE_VILLAGE'] ?? '';
        $info['residence_alley']= $items['RESIDENCE_ALLEY'] ?? '';
        $info['residence_street']= $items['RESIDENCE_STREET'] ?? '';
        $info['register_detail_address'] = $items['REGISTER_DETAIL_ADDRESS'] ?? ''; //户口地详情
        $info['residence_detail_address'] = $items['RESIDENCE_DETAIL_ADDRESS'] ?? ''; //居住地详情

        //印尼增加村级别选择
        if (YII_COUNTRY == "ID") {
            $info['register_rt']  = $items['REGISTER_RT'] ?? '';  //户口所在地邻组
            $info['register_rw']  = $items['REGISTER_RW'] ?? '';  //户口所在地居委会
            $info['residence_rt'] = $items['RESIDENCE_RT'] ?? ''; //居住地邻组
            $info['residence_rw'] = $items['RESIDENCE_RW'] ?? ''; //居住地居委会

            $villageCodes = $villageListKy = [];

            //开户地村庄特殊处理
            if ($info['register_country'] == $default_country_value) {
                $villageCodes[] = $info['register_village'];
            } else {
                $info['register_village_name'] = $info['residence_village'] ?? '';
            }

            //居住地村庄特殊处理
            if ($info['residence_country'] == $default_country_value) {
                $villageCodes[] = $info['residence_village'];
            } else {
                $info['residence_village_name'] = $info['residence_village'] ?? '';
            }

            if (!empty($villageCodes)) {
                $villageList   = SysArea::getVillageByCodes($villageCodes);
                $villageListKy = array_column($villageList, 'name', 'code');
            }

            if (empty($info['register_village_name'])) {
                $info['register_village_name'] = $villageListKy[$info['register_village']] ?? '';
            }

            if (empty($info['residence_village_name'])) {
                $info['residence_village_name'] = $villageListKy[$info['residence_village']] ?? '';
            }
        }

        //家庭信息
        $info['dad_first_name']= $items['DAD_FIRST_NAME'] ?? '';
        $info['dad_last_name']= $items['DAD_LAST_NAME'] ?? '';
        $info['mum_first_name']= $items['MUM_FIRST_NAME'] ?? '';
        $info['mum_last_name']= $items['MUM_LAST_NAME'] ?? '';
        $info['relatives_relationship']= $items['RELATIVES_RELATIONSHIP'] ?? '';
        $info['relatives_first_name']= $items['RELATIVES_FIRST_NAME'] ?? '';
        $info['relatives_last_name']= $items['RELATIVES_LAST_NAME'] ?? '';
        $info['relatives_call_name']= $items['RELATIVES_CALL_NAME'] ?? '';
        $info['relatives_mobile']= $items['RELATIVES_MOBILE'] ?? '';
        $info['working_country'] = $items['WORKING_COUNTRY'] ?? ''; //工作所在国家
        $race = $items['RACE'] ?? '';//种族
        $religion = $items['RELIGION'] ?? '';//宗教
        $info['race'] = empty($race) ? '' : (int)$race;//种族
        $info['religion'] = empty($religion) ? '' : (int)$religion;//宗教
	    $info['staff_province_code'] =  $items['STAFF_PROVINCE_CODE'] ?? ''; //工作所在洲 只有马来 网点 为-1 的时候  有值
        $info['bank_branch_name'] = $items['BANK_BRANCH_NAME'] ?? '';//银行分行名称
        $info['household_registration'] = $items['HOUSEHOLD_REGISTRATION'] ?? '';//户籍照号
        $info['ptkp_state'] = $items['PTKP_STATE'] ?? '';//PTKP状态
        $info['tax_card'] = $items['TAX_CARD'] ?? '';//税卡号
        if(YII_COUNTRY == 'PH'){
            $info['conversion_permanent_date'] = $items['CONVERSION_PERMANENT_DATE'] ?? null;//转正式工日期  菲律宾
        }

        if (in_array($info['formal'], [1, 4])) {
            $info['hire_type_text'] = Yii::$app->lang->get('hire_type_' . $info['hire_type']);
        } else {
            $info['hire_type_text'] = '';
        }

        $payment_markup_arr = array_filter(explode(',',$info['payment_markup']));
        $payment_markup_name_arr = [];
        foreach ($payment_markup_arr as $k => $v) {
            $paymentMarkupKey = Staff::getPaymentRemark($v);
            $payment_markup_name_arr[] = Yii::$app->lang->get($paymentMarkupKey);
        }
        $info['payment_markup_name_arr'] = implode(',',$payment_markup_name_arr);

        if ($items['IDENTITY_FRONT_KEY'] ?? false) {
            $info['identity_front_key'] = $items['IDENTITY_FRONT_KEY'] ?? '';
            $info['identity_front'] = Yii::$app->params['hris_profile_uri'] . $items['IDENTITY_FRONT_KEY'] ?? '';
        }

        if ($items['IDENTITY_REVERSE_KEY'] ?? false) {
            $info['identity_reverse_key'] = $items['IDENTITY_REVERSE_KEY'] ?? '';
            $info['identity_reverse'] = Yii::$app->params['hris_profile_uri'] . $items['IDENTITY_REVERSE_KEY'] ?? '';
        }

        if (!empty($items['PROFILE_OBJECT_KEY'])) {
            $info['staff_profile'] = Yii::$app->params['hris_profile_uri'] . $items['PROFILE_OBJECT_KEY'];
            $info['profile_object_key'] = $items['PROFILE_OBJECT_KEY'];
        }

        $info['position_category'] = $model->getPositionCategory();
        $position_category_ids =  $info['position_category'];
        $position_category_name = '';
        array_walk($position_category_ids, function($value, $key) use (&$position_category_name){
            $position_category_name .= Yii::$app->lang->get('role_'.$value).'  ';
        });
        $info['position_category_name'] = $position_category_name;

        $areaRoles = (array)explode(",", SettingEnvService::getInstance()->getSetVal('jurisdiction_area_roles'));
        if (count(array_intersect($areaRoles, $info['position_category'])) > 0) {
            $info['manage_area_name'] = array_map('intval',
                HrAreaManagerStore::find()->select('DISTINCT(manage_area_name)')->where(['staff_info_id' => $staffInfoId])->column());
        }

        //职位名称
        if(!empty($info['job_title'])) {
            $jobs = StaffManager::getAllJobTitleByIds([$info['job_title']]);
            $info['job_title_is_deleted'] = 1;
            if($jobs){
                $info['job_title_is_deleted'] = (int) $jobs[$info['job_title']]['status']; // 1开启 2 关闭
                $info['job_title_name'] = $jobs[$info['job_title']]['job_name'] ?? '';
                if (!empty($info['job_title_name']) && HrJobTitle::STATUS_2 == $jobs[$info['job_title']]['status']) {
                    $info['job_title_name'] .= Yii::$app->lang->get('deleted');
                }
            }
        }

        //部门名称
        $res = [];
        if(!empty($info['sys_department_id'])) {
            $info['sys_department_is_deleted']  = 0;
            $info['sys_department_name']        = '';
            $department = SysGroupDeptV2::getAllDepartmentByIds([],[$info['sys_department_id']]);
            if(isset($department[$info['sys_department_id']])) {
                $info['sys_department_is_deleted'] = (int)$department[$info['sys_department_id']]['deleted']; // 0 未删除 1 已删除
                $info['sys_department_name']       = $department[$info['sys_department_id']]['name'] ?? '';
                if (!empty($info['sys_department_name']) && SysDepartment::DELETE_1 == $department[$info['sys_department_id']]['deleted']) {
                    $info['sys_department_name'] .= Yii::$app->lang->get('deleted');
                }
            }
        }

        if(!empty($info['node_department_id'])) {
            $department = SysGroupDeptV2::getAllDepartmentByIds([$info['node_department_id']]);
            if(isset($department[$info['node_department_id']])) {
                $info['node_department_is_deleted'] = (int) $department[$info['node_department_id']]['deleted']; // 0 未删除 1 已删除
                $info['node_department_name'] = $department[$info['node_department_id']]['name'] ?? '';
                if (!empty($info['node_department_name']) && SysDepartment::DELETE_1 == $department[$info['node_department_id']]['deleted']) {
                    $info['node_department_name'] .= Yii::$app->lang->get('deleted');
                }
            } else {
                $info['node_department_is_deleted'] = 0;
                $info['node_department_name'] = '';
            }
        }

        if (!empty($info['contract_company_id'])) {
            $info['contract_company_name'] = CompanyService::getInstance()->getContractCompanyMap()[$info['contract_company_id']] ?? '';
        }

        if(!empty($info['sys_department_id']) && $info['node_department_id'] != 0) {
            //Yii::$app->sysconfig->_getNodeLevel(Yii::$app->sysconfig->allDepeartments_tree,'ancestry',$info['node_department_id'],$res);
            //array_push($res,$info['node_department_id']);
            $dept_detail = SysGroupDeptV2::getDeptDetail($info['node_department_id']);
            $res = $info['node_department_id'] == 999 ? [999] : $dept_detail['ancestry_v3_arr'] ?? [];
        } else {
            array_push($res,(int)$info['sys_department_id']);
        }
        $info['department_level'] = $res;
        //职等
        $info['job_title_level_name'] = empty($info['job_title_level']) ? null : (Yii::$app->sysconfig->config_job_title_level[$info['job_title_level']] ?? null);


        //网点名称
        if(!empty($info['sys_store_id'])) {
            if($info['sys_store_id'] == '-1') {
                $info['sys_store_name'] = Yii::$app->lang->get('head_office');
            } else {
                $store_info = self::getStoreInfo($info['sys_store_id']);
                $info['sys_store_name'] = $store_info['name'] ?? '';
            }
        }

        if ($model->isInFormal()) {
            $info['pay_type'] = $items['PAY_TYPE'] ?? '';
            $info['supervisor_mobile'] = $items['SUPERVISOR_MOBILE'] ?? '';
        }else{
            //获取车辆管理表中车辆信息
            $vehicle_info = self::getVehicleInfo($staffInfoId);
            $info = array_merge($info,$vehicle_info);
        }
        $info['vehicle_source'] = !empty($info['vehicle_source']) ? strval($info['vehicle_source']) : '';


        if ($model->isFormal()) {
            // 主账号
            if (!empty($items['MASTER_STAFF'])) {
                $info['master_staff'] = $items['MASTER_STAFF'];
            } else {
                $storeTemp = SysStoreTemp::temp();
                //子账号
                foreach ($model->subStaffs as $key => $subModel) {
                    $info['sub_staffs'] = [];
                }
            }
        }

        // 出纳判断
        if (in_array(Yii::$app->sysconfig->getIdRole('STORE_CASHIER'), $info['position_category'])) {
            $info['has_remittance'] = 1;
            if (!StoreRemittanceBill::find()->where(['store_id' => $info['sys_store_id'], 'state' => 0])->exists()) {
                if (!StoreRemittanceRecord::find()->where(['store_id' => $info['sys_store_id'], 'state' => 0])->exists()) {
                    $info['has_remittance'] = 0;
                }
            }
        }

        // 直线主管
        if (!empty($items['MANGER'])) {
            $info['manager'] = $items['MANGER'];
            $staff = StaffInfo_fle::find()->where(['id' => (string) $info['manager']])->asArray()->one();
            $info['manager_name'] = $staff['name'] ?? '';
            $info['manager_state'] = $staff['state'] ?? '';//直线主管在职状态
        }

        // 虚线主管
        if (!empty($items['INDIRECT_MANGER'])) {
            $info['indirect_manager'] = $items['INDIRECT_MANGER'];
            // $staff = StaffInfo::find()->select('name')->where(['staff_info_id' => $info['indirect_manager']])->asArray()->one();
            // $info['indirect_manager_name'] = $staff['name'] ?? '';
        }

        //辅导员
        $info['instructor_name'] = '';
        if(!empty($info['instructor_id'])) {
            $instructor_info = BaseStaffInfo::find()->select(['name'])->where(['staff_info_id' => $info['instructor_id']])->one(Yii::$app->get('r_backyard'));
            if($instructor_info) {
                $info['instructor_name'] = $instructor_info->name;
            }
        }

        if(!empty($info['stop_payment_type'])) {
            $info['stop_payment_type_arr'] = explode(',',$info['stop_payment_type']);
            foreach ($info['stop_payment_type_arr'] as $k => $v) {
                $info['stop_payment_type_name_arr'][] = Yii::$app->lang->get('stop_payment_type_' . $v);
            }
        } else {
            $info['stop_payment_type_arr'] = [];
        }

        //员工绑定服务区
        $sys_district = SysDistrict::find()->where(['courier_id' => $model->staff_info_id, 'deleted' => 0])->one();
        $info['sys_district'] = $sys_district['code'] ?? '0';

        //邮箱状态
        $email_model = HrEmails::find()->where(['staff_info_id' => $staffInfoId])->one();

        if ($email_model && !empty($info['email'])) {
            $info['email_state'] = $email_model->state;
            $email_arr           = explode('@', $info['email']);
            //后缀展示
            $suffixList           = array_column((SysConfigService::getInstance())->getEmailSuffixList(), 'value', 'key');
            $info['email_suffix'] = array_search('@' . $email_arr[1], $suffixList);
        } else {
            $info['email']        = '';
            $info['email_state']  = '-1';
            $info['email_suffix'] = '-1';
        }

        $ret = [
            'img_driving_license'   => [],
            'img_identity'          => [],
            'img_driver_license'    => [],
            'img_bank_info'         => [],
            'img_temp_contract'     => [],
            'img_household_registry'=> [],
            'img_vehicle'           => [],
        ];
        $info_attachment = StaffAttachment::find()->where(['oss_bucket_key' => $staffInfoId, 'deleted' => 0])->all(Yii::$app->get('r_backyard'));
        if ($info_attachment) {
            foreach ($info_attachment as $v) {
                $images = [];
                switch ($v['oss_bucket_type']) {
                    case 'IMG_DRIVING_LICENSE':
                    case 'IMG_IDENTITY':
                    case 'IMG_DRIVER_LICENSE':
                    case 'IMG_BANK_INFO':
                    case 'IMG_TEMP_CONTRACT':
                    case 'IMG_HOUSEHOLD_REGISTRY':
                    case 'IMG_VEHICLE':
                        $images = [
                            'url'        => Yii::$app->params['hris_profile_uri'] . $v['object_key'],
                            'object_key' => $v['object_key'],
                        ];
                        break;
                    default:
                        break;
                }
                $objectKey = strtolower($v['oss_bucket_type']);
                array_push($ret[$objectKey], $images);
            }
        }
        $info = array_merge($info, $ret);

        //管辖大区、片区
        $regions_list = HrStaffManageRegions::find()->select(['region_id','piece_id'])->where(['staff_info_id' => $staffInfoId])->asArray()->all();

        /*
        if (!empty($regions_list)) {
            $info['manage_region_and_piece'] = [];
            $info['region_id'] = $regions_list['region_id'] ?? 0;
            $info['piece_id']  = $regions_list['piece_id'] ?? 0;
            if ($info['region_id'] !== 0) {
                array_push($info['manage_region_and_piece'], $info['region_id']);
            }
            if ($info['piece_id'] !== 0) {
                array_push($info['manage_region_and_piece'], $info['piece_id']);
            }
        } else {
            $info['manage_region_and_piece'] = [];
        }
        */
        if(!empty($regions_list)) {
            $manage_region = array_unique(array_column($regions_list,'region_id'));
            $manage_piece = [];
            foreach ($regions_list as $key => $value) {
                if($value['piece_id'] != 0) {
                    $manage_piece[] = $value['piece_id'];
                }
            }
            if(count($manage_piece) > 0) {
                $manage_region = [];
            }

            $info['region_id'] = $manage_region;
            $info['piece_id'] = $manage_piece;
        } else {
            $info['region_id'] = [];
            $info['piece_id'] = [];
        }

        //身份证照片显示
        $identity_annex_info        = HrStaffAnnexInfoService::getInstance()->getHrStaffAnnexInfoByStaffIds([$staffInfoId]);
        $info['identity_annex_url'] = '';
        if (!empty($identity_annex_info[$staffInfoId][HrStaffAnnexInfo::TYPE_ID_CARD]) && $identity_annex_info[$staffInfoId][HrStaffAnnexInfo::TYPE_ID_CARD]['audit_state'] == 1) {
            $info['identity_annex_url'] = $identity_annex_info[$staffInfoId][HrStaffAnnexInfo::TYPE_ID_CARD]['annex_path_front'];
        }

        $info['hand_identity_url'] = '';
        if(isCountry('PH')) {
            $resumeExtend = ResumeService::getInstance()->getResumeExtendInfo($staffInfoId);
            $info['hand_identity_url'] = empty($resumeExtend['hand_identity_url']) ? '' : $resumeExtend['hand_identity_url'];
        }


        $info['residence_booklet_file_url_first'] = '';
        $info['residence_booklet_file_url_second'] = '';
        $info['residence_booklet_audit'] = HrStaffAnnexInfo::AUDIT_STATE_TO_BE_UPLOAD;
        if (!empty($identity_annex_info[$staffInfoId][HrStaffAnnexInfo::TYPE_RESIDENCE_BOOKLET_CARD]) && $identity_annex_info[$staffInfoId][HrStaffAnnexInfo::TYPE_RESIDENCE_BOOKLET_CARD]['audit_state'] == 1) {
            $info['residence_booklet_file_url_first'] = $identity_annex_info[$staffInfoId][HrStaffAnnexInfo::TYPE_RESIDENCE_BOOKLET_CARD]['annex_path_front'];
            $info['residence_booklet_file_url_second'] = $identity_annex_info[$staffInfoId][HrStaffAnnexInfo::TYPE_RESIDENCE_BOOKLET_CARD]['annex_path_rear'];
            $info['residence_booklet_audit'] = $identity_annex_info[$staffInfoId][HrStaffAnnexInfo::TYPE_RESIDENCE_BOOKLET_CARD]['audit_state'];
        }
        $cycle_list = StaffHold::getStaffHoldCycleList(['staff_info_ids' => [$staffInfoId]]);
        $cycle = $cycle_list[$staffInfoId] ?? '';
        $info['staff_hold_cycle'] = !empty($cycle) ? implode(',', $cycle) : ''; //hold周期

        //工作天数&轮休规则
        //week_working_day 每周工作天数
        //rest_type 轮休规则
        if(!empty($info['week_working_day']) && !empty($info['rest_type'])) {
            $working_day_rest_type = $info['week_working_day'] . $info['rest_type'];
            $info['working_day_rest_type'] = $working_day_rest_type;
            $info['working_day_rest_type_text'] = Yii::$app->lang->get('working_day_rest_type_'.$working_day_rest_type);
        } else {
            $info['working_day_rest_type'] = '';
            $info['working_day_rest_type_text'] = '';
        }

        $info['hire_date'] = !empty($info['hire_date']) ? date('Y-m-d', strtotime($info['hire_date'])) : '';
        $info['leave_date'] = !empty($info['leave_date']) ? date('Y-m-d', strtotime($info['leave_date'])) : '';
        $info['stop_duties_date'] = !empty($info['stop_duties_date']) ? date('Y-m-d', strtotime($info['stop_duties_date'])) : '';

        if (YII_COUNTRY == 'TH'){
            $info['social_security_leave_date']= $items['SOCIAL_SECURITY_LEAVE_DATE'] ?? '';
        }

        if(isCountry('TH') && $info['formal'] == BaseStaffInfo::FORMAL_OUTSOURCE) {
            $osExtendInfo = OsStaffInfoExtend::find()->where(['staff_info_id' => $staffInfoId])->asArray()->one();
            $info['company_item_id']= empty($osExtendInfo['company_item_id']) ? NULL : intval($osExtendInfo['company_item_id']);
        }

        // unset($info['creater']);
        unset($info['created_at']);
        $info['is_show_project_num'] = false;
        $extendInfo          = HrStaffInfoExtend::find()->where(['staff_info_id' => $staffInfoId])->asArray()->one();
        $info['project_num'] = $extendInfo['project_num'] ?? null;
        if (in_array($info['job_title'], VehicleInfoService::getInstance()->getVehicleTypeJobTitle())) {
            $info['is_show_project_num'] = $info['project_num'] > 0;
        }
        //新增字段 车厢状态 开始结束时间
        if (YII_COUNTRY == 'TH' && $info['job_title'] == 110 && !in_array($info['hire_type'], BaseStaffInfo::$agentTypeTogether)) {
            $info['van_container_state'] = ($extendInfo['van_container_state'] == 1) ? Yii::$app->lang->get('have_container') : Yii::$app->lang->get('have_no_container');
            if(is_null($extendInfo['van_container_state'])){
                $info['van_container_state'] = '';
            }
            $info['van_container_start'] = $extendInfo['van_container_start'] ?? '';
            $info['van_container_end']   = $extendInfo['van_container_end'] ?? '';
        }

        return $info;
    }

    /**
     * //获取正式员工车辆信息
     * @param $uid
     */
    public static function getVehicleInfo($uid){

        $vehicle_info = [
        ];

        $vehicle_model = VehicleInfo::find()->where(['uid' => $uid])->one();
        if($vehicle_model && $vehicle_model->formal_data){
            $vehicle_formal_data = json_decode($vehicle_model->formal_data,true);
            $vehicle_info = [
                'staff_car_type'=>  VehicleInfo::$staffCarTypeMap[$vehicle_model->vehicle_type]??'',
                'staff_car_no'=>$vehicle_formal_data['plate_number'] ?? null,
                'vehicle_source'=>$vehicle_formal_data['vehicle_source']??null ,
                'vehicle_use_date'=> $vehicle_formal_data['vehicle_start_date'] ?? null
            ];
        }
        return $vehicle_info;
    }

    public static function getStaffByStoreRole($storeId, $roleid)
    {
        $result = StaffInfo::find()
            ->select(['hr_staff_info.staff_info_id', 'hr_staff_info.name'])
            ->innerJoin('hr_staff_info_position', 'hr_staff_info_position.staff_info_id = hr_staff_info.staff_info_id')
            ->where(['hr_staff_info.sys_store_id' => $storeId])
            ->andWhere(['hr_staff_info.state' => 1])
            ->andWhere(['hr_staff_info.formal' => 1])
            ->andWhere(['hr_staff_info_position.position_category' => $roleid])
            ->andWhere(['hr_staff_info.is_sub_staff' => 0])
            ->asArray()
            ->all();
        return $result;
    }

    public static function getStoreManager($storeId)
    {
        $result = self::getStaffByStoreRole($storeId, \Yii::$app->sysconfig->getIdRole('STORE_MANAGER'));
        return $result[0] ?? [];
    }

    public static function getStoreSupervisor($storeId)
    {
        $result = self::getStaffByStoreRole($storeId, \Yii::$app->sysconfig->getIdRole('STORE_SUPERVISOR'));
        return $result[0] ?? [];
    }

    public static function getAreaStoreManager($storeId)
    {
        $result = self::getStaffByStoreRole($storeId, \Yii::$app->sysconfig->getIdRole('STORE_MANAGER'));
        return $result[0] ?? [];
    }

    public static function getDefaultManager($depeartmentId, $storeId, $roles)
    {
        // header office
        if ($storeId == -1) {
            // 部门header office
            $default = [
                'staff_info_id' => Yii::$app->sysconfig->depeartmentManager[$depeartmentId] ?? '',
                'is_default' => 1,
            ];
            return $default;
        }

        $stores = SysStoreTemp::temp();
        if (!($store = ($stores[$storeId] ?? []))) {
            return [];
        }

        // 区域经理
        if (in_array(Yii::$app->sysconfig->getIdRole('AREA_MANAGER'), $roles)) {
            $default['staff_info_id'] = Yii::$app->sysconfig->depeartmentManager[$depeartmentId] ?? '';
        } else if (in_array($store['category'], ['1', '2','8'])) {
            // 分拨中心，收派件网点、Hub
            if (in_array(Yii::$app->sysconfig->getIdRole('STORE_MANAGER'), $roles)) {
                // 1.网点经理
                $default['staff_info_id'] = Yii::$app->sysconfig->areaManager[$store['province_code']] ?? '';
            } else if (in_array(Yii::$app->sysconfig->getIdRole('STORE_SUPERVISOR'), $roles)) {
                // 2.网点主管 -> 网点经理  -> 区域经理
                $default = self::getStoreManager($store['id']);
                if (empty($default)) {
                    $default['staff_info_id'] = Yii::$app->sysconfig->areaManager[$store['province_code']] ?? '';
                }
            } else {
                // 3.其他人员 -> 网点经理- -> 网点主管
                $default = Staff::getStoreManager($store['id']);
                if (empty($default)) {
                    $default = Staff::getStoreSupervisor($storeId);
                }
            }
        } else if (in_array($store['category'], ['4', '5','7'])) {
            $default_staff_info_id = 17109;
            if($store['category'] == '7') {
                $default_staff_info_id = 17117;
            }

            if (in_array(Yii::$app->sysconfig->getIdRole('STORE_MANAGER'), $roles)) {
                // 1.网点经理
                $default['staff_info_id'] = $default_staff_info_id;
            } else if (in_array(Yii::$app->sysconfig->getIdRole('STORE_SUPERVISOR'), $roles)) {
                // 2.网点主管 -> 网点经理  -> 区域经理
                $default = self::getStoreManager($store['id']);
                if (empty($default)) {
                    $default['staff_info_id'] = $default_staff_info_id;
                }
            } else {
                // 3.其他人员 -> 网点经理- -> 网点主管
                $default = Staff::getStoreManager($store['id']);
                if (empty($default)) {
                    $default = Staff::getStoreSupervisor($storeId);
                }
            }
        }

        if (!empty($default)) {
            $default['is_default'] = 1;
        }
        return $default ?? [];
    }

    //$is_ms  == 2 的时候代表 ms 操作 回调  不发送离职邮件
    public static function updateItems($staffInfoId, $attributes, $operaterId = -1, $reset_password = false, $is_ms = 1)
    {
        $staffInfoId = intval($staffInfoId);
        $baseStaff = BaseStaffInfo::find()->where(['staff_info_id' => $staffInfoId])->one();
        Yii::$app->logger->write_log(' updateItems '.json_encode(['staffInfoId'=>$staffInfoId, 'attributes'=>$attributes, 'operaterId'=>$operaterId, 'reset_password'=>$reset_password,'is_ms'=>$is_ms], JSON_UNESCAPED_UNICODE),'info');

        if (!$baseStaff) {
            return true;
        }

        //FlashHR同步过来的员工申请离职的日期
        $flash_hr_apply_resign_time = null;
        if (!empty($attributes['flash_hr_apply_resign_time'])) {
            $flash_hr_apply_resign_time = $attributes['flash_hr_apply_resign_time'];
            unset($attributes['flash_hr_apply_resign_time']);
        }

        $face_blacklist_identity = null;
        if (!empty($attributes['face_blacklist_identity'])) {
            $face_blacklist_identity = $attributes['face_blacklist_identity'];
            unset($attributes['face_blacklist_identity']);
        }

        // 允许更新为空的字段
        $emptyFields = !empty($attributes['empty_field']) ? $attributes['empty_field'] : [];
        $changeAtt = [];
        $changeItems = [];
        $changePositions = [];

        $valid_column = [
            'identity',
            'bank_no',
            'state',
            'leave_date',
            'stop_duties_date',
            'mobile',
            'email',
            'personal_email',
            'is_auto_system_change',
            'payment_state',
            'payment_markup',
            'oil_card_deposit',
            'bank_type',
            'leave_reason',
            'leave_type',
            'wait_leave_state',
            'job_title',
            'sys_department_id',
            'node_department_id',
            'stop_duty_reason',
            'stop_payment_type',
            'mobile_company',
            'job_title_level',
            'job_title_grade',
            'job_title_grade_v2',
            'name',
            'remarks',
            'leave_source',
            'nick_name',
            'name_en',
            'sex',
            'hire_date',
            'instructor_id',
            'sys_store_id',
            'working_country',
            'manger',
            'education',
            'nationality',
            'is_has_job_grade_permission',
            'job_grade_edit_permission',
            'first_name',
            'middle_name',
            'last_name',
            'suffix_name',
            'social_security_num',
            'medical_insurance_num',
            'fund_num',
            'company_name_ef',
            'week_working_day',
            'rest_type',
            'is_history_leave',
            'social_security_url',
            'medical_insurance_url',
            'fund_url',
            'tax_card_url',
            'contract_expiry_date',
            'hire_times',
            'job_grade_effective_date',
        ];
        if (YII_COUNTRY == 'PH') {
            $valid_column[] = 'hire_type';
        }
        foreach ($valid_column as $attr) {
            if (!empty($attributes[$attr])) {
                $changeAtt[$attr] = trim($attributes[$attr]);
            }

            if (in_array($attr,$emptyFields)) { // 允许为空字段的更新
                $changeAtt[$attr] = "";
            }

            if (isset($attributes['personal_email']) && !$attributes['personal_email']) {
                $changeAtt['personal_email'] = '';
            }

//            if (isset($attributes['nick_name']) && !$attributes['nick_name']) {
//                $changeAtt['nick_name'] = '';
//            }

            if (isset($attributes['is_auto_system_change']) && !$attributes['is_auto_system_change']) {
                $changeAtt['is_auto_system_change'] = 0;
            }

            if(isset($attributes['mobile_company']) && !$attributes['mobile_company']) {
                $changeAtt['mobile_company'] = '';
            }
            if (isset($attributes['is_has_job_grade_permission'])) {
                $changeAtt['is_has_job_grade_permission'] = $attributes['is_has_job_grade_permission'];
            }
            if (isset($attributes['job_grade_edit_permission'])) {
                $changeAtt['job_grade_edit_permission'] = $attributes['job_grade_edit_permission'];
            }

            if(isset($attributes['job_title_grade_v2'])) {
                $changeAtt['job_title_grade_v2'] = trim($attributes['job_title_grade_v2']);
            }

            if(isset($attributes['job_grade_effective_date'])) {
                $changeAtt['job_grade_effective_date'] = $attributes['job_grade_effective_date'];
            }

            if(isset($attributes['wait_leave_state'])) {
                $changeAtt['wait_leave_state'] = $attributes['wait_leave_state'];
            }
            if (isset($attributes['stop_duties_date'])) {
                $changeAtt['stop_duties_date'] = substr($attributes['stop_duties_date'], 0, 10);
            }
        }
        
        if (!empty($changeAtt['bank_no']) && !empty($changeAtt['bank_type'])){
            if(($res = StaffInfoCheck::validateBankNoStingLen($changeAtt['bank_no'], $changeAtt['bank_type'])) && $res !== true) {
                return $res;
            }
        }

        // 菲律宾验证备用银行卡号
        if (YII_COUNTRY == 'PH' && !empty($attributes['backup_bank_no']) && !empty($attributes['backup_bank_type'])) {   // 菲律宾需要验证备用银行卡信息
            if(($res = StaffInfoCheck::validateBankNoStingLen($attributes['backup_bank_no'], $attributes['backup_bank_type'],'backup_bank_no')) && $res !== true) {
                return $res;
            }
        }

        if (isset($changeAtt['state'])) {
            if ($changeAtt['state'] == 2) {
                if (empty($changeAtt['leave_date'])) {
                    return ['leave_date', 'common_empty_error'];
                }
                $changeAtt['wait_leave_state'] = 0;
                $changeAtt['is_history_leave'] = 1;
                //if (!Yii::$app->util->dateValidate($changeAtt['leave_date'], 'yyyy-MM-dd') && !Yii::$app->util->dateValidate($changeAtt['leave_date'], 'yyyy-MM-dd HH:mm:ss')) {
                //    return ['leave_date', 'date format error,correct format "2012-12-12"'];
                //}
                //$changeAtt['stop_duties_date'] = null;
            } else if ($changeAtt['state'] == 1) {
                $changeAtt['stop_duties_date'] = null;
                $changeAtt['stop_duty_reason'] = null;
                if (isset($changeAtt['wait_leave_state'])) {
                    $changeAtt['wait_leave_state'] = intval($attributes['wait_leave_state']);
                    $leave_manage_detail = LeaveManager::find()->where(['staff_info_id' => $staffInfoId])->one();
                    $changeAtt['is_history_leave'] = $leave_manage_detail ? 1:0;
                    if ($changeAtt['wait_leave_state'] == 0) {
                        $changeAtt['leave_date']   = null;
                        $changeAtt['leave_reason'] = null;
                        $changeAtt['leave_type']   = null;
                        $changeAtt['leave_source'] = 0;
                    }
                } elseif ($baseStaff->wait_leave_state == 0 && $baseStaff->state != BaseStaffInfo::STATE_SUSPENSION) {
                    $changeAtt['leave_date']   = null;
                    $changeAtt['leave_reason'] = null;
                    $changeAtt['leave_type']   = null;
                    $changeAtt['leave_source'] = 0;
                }
                if(isset($attributes['leave_source'])){
                    $changeAtt['leave_source'] = intval($attributes['leave_source']);
                }

            } else if ($changeAtt['state'] == 3) {
                if ($baseStaff->state == BaseStaffInfo::STATE_SUSPENSION) {
                    return ['state', 'The employee has been suspended from work'];
                }
                $changeAtt['stop_duties_date'] = $changeAtt['stop_duties_date'] ?? (new \DateTime('now', new \DateTimeZone(TIMEZONE)))->format('Y-m-d');
                $baseStaff->stop_duties_count++;
                $changeAtt['stop_duties_count'] = $baseStaff->stop_duties_count;
                //$changeAtt['leave_date'] = null;
                $changeAtt['wait_leave_state'] = 0;
            }
        }

        $item_column = [
            'MANGER','CAR_TYPE','CAR_NO','BIRTHDAY','RESIDENCE_HOUSE_NUM','RESIDENCE_VILLAGE_NUM','RESIDENCE_VILLAGE','RESIDENCE_ALLEY','RESIDENCE_STREET','RESIDENCE_POSTCODES'
            ,'REGISTER_HOUSE_NUM','REGISTER_VILLAGE_NUM','REGISTER_VILLAGE','REGISTER_ALLEY','REGISTER_STREET','REGISTER_POSTCODES'
            ,'RELATIVES_FIRST_NAME','RELATIVES_LAST_NAME','RELATIVES_MOBILE','DAD_FIRST_NAME','DAD_LAST_NAME','MUM_FIRST_NAME','MUM_LAST_NAME','EDUCATION','GRADUATE_SCHOOL','MAJOR','GRADUATE_TIME'
            ,'WORKING_COUNTRY','REGISTER_DETAIL_ADDRESS','RESIDENCE_DETAIL_ADDRESS','NATIONALITY'
            ,'BANK_BRANCH_NAME','HOUSEHOLD_REGISTRATION','PTKP_STATE','TAX_CARD','BANK_NO_NAME','BACKUP_BANK_NO_NAME'
        ];
        //随意更改的 可以为空的
        $item_casually = [
            'RESIDENCE_COUNTRY','RESIDENCE_PROVINCE','RESIDENCE_CITY','RESIDENCE_DISTRICT','RESIDENCE_DISTRICT','RESIDENCE_POSTCODES'
            ,'RESIDENCE_HOUSE_NUM','RESIDENCE_VILLAGE_NUM','RESIDENCE_VILLAGE','RESIDENCE_ALLEY','RESIDENCE_STREET','RESIDENCE_DETAIL_ADDRESS'
            ,'RACE','RELIGION'
            ,'REGISTER_DISTRICT','REGISTER_CITY','REGISTER_PROVINCE','REGISTER_COUNTRY','MEDICAL_INSURANCE','SOCIAL_SECURITY'
        ];

        $item_column = array_merge($item_column,$item_casually);

        if (YII_COUNTRY == 'PH') {
           $item_column[] = 'BACKUP_BANK_NO';
           $item_column[] = 'CONVERSION_PERMANENT_DATE'; //月薪制合同工转正日期
        }

        if (YII_COUNTRY == 'ID') {
            array_push($item_column, 'REGISTER_RT');
            array_push($item_column, 'REGISTER_RW');
            array_push($item_column, 'RESIDENCE_RT');
            array_push($item_column, 'RESIDENCE_RW');
        }

        if (YII_COUNTRY == 'MY') {
            $item_column = array_merge($item_column,[
                'RELATIVES_MOBILE',
                'RELATIVES_CALL_NAME',
                'RELATIVES_LAST_NAME',
                'RELATIVES_FIRST_NAME',
                'RELATIVES_RELATIONSHIP'
            ]);
        }

        foreach ($item_column as $attr) {
            if (!empty($attributes[strtolower($attr)])) {
                $changeItems[$attr] = trim($attributes[strtolower($attr)]);
            }
            if (in_array(strtolower($attr),$emptyFields)) { // 允许为空字段的更新
                $changeItems[$attr] = "";
            }
            if(isset($attributes[strtolower($attr)]) && in_array($attr,$item_casually) ){
                $changeItems[$attr] = trim($attributes[strtolower($attr)]);
            }
        }

        //可更新角色
        if (isset($attributes['position_category']) && !empty($attributes['position_category'])) {
            $changePositions = $attributes['position_category'];
        }
        if (empty($changeAtt) && empty($changeItems) && empty($changePositions)) {
            return ['not need update'];
        }

        //如果企业号码不为空验证号码是否有其他工号在使用
        if(!empty($changeAtt['mobile_company'])) {

            if(YII_COUNTRY == 'ID'){
                if(!preg_match("/(^[0-9]{8,13}$)/u", $changeAtt['mobile_company'])) {
                    return ['The Mobile Number must be 8-13 digits'];
                }
                if (strlen($changeAtt['mobile_company']) != 13 && strpos($changeAtt['mobile_company'], "0") !== 0 ) {
                    $changeAtt['mobile_company'] = str_pad($changeAtt['mobile_company'], strlen($changeAtt['mobile_company'])+1, "0", STR_PAD_LEFT);
                }
            }else{
                $mobileMaxLen = 10;
                if (in_array(YII_COUNTRY, ['PH', 'LA', 'MY'])) {

                    $mobileMaxLen = 11;
                }
                if(!preg_match("/(^[0-9]{10,{$mobileMaxLen}}$)/u", $changeAtt['mobile_company'])) {
                    return ['mobile company wrong format'];
                }
            }

            if (BaseStaffInfo::find()
                ->where(['mobile_company' => $changeAtt['mobile_company']])
                ->andWhere(['state' => [BaseStaffInfo::STATE_ON_JOB, BaseStaffInfo::STATE_SUSPENSION]])
                ->andWhere(['hire_type' => BaseStaffInfo::$company_staff_hire_type])
                ->andWhere(['is_sub_staff' => 0])
                ->andWhere(['<>', 'staff_info_id', $staffInfoId])->exists(Yii::$app->get('r_backyard'))) {
                return ['mobile_company_existed'];
            }
        }

        //如果辅导员id不为空的时候校验辅导员id是否正常
        if(isset($changeAtt['instructor_id']) && !empty($changeAtt['instructor_id'])) {
            $instructor_exists = BaseStaffInfo::find()
                ->where(['staff_info_id' => $changeAtt['instructor_id']])
                ->andWhere(['state' => 1])
                ->andWhere(['formal' => 1])
                ->exists(Yii::$app->get('r_backyard'));
            if(!$instructor_exists) {
                return ['instructor not existed'];
            }
        }
        Yii::$app->logger->write_log(['changeAtt' => $changeAtt], 'info');
        $before = self::view($staffInfoId);

        $manager_store_list = [];
        if(in_array(YII_COUNTRY, ['TH', 'PH', 'MY']) && isset($changeAtt['state']) && $changeAtt['state'] == 2) {
            $manager_store_list = SysStore::find()->where(['manager_id' => $staffInfoId])->andWhere(['state' => 1])->andWhere(['category' => StaffService::getInstance()->change_store_manager_store_category])->asArray()->all(Yii::$app->get('r_backyard'));
        }
        $is_lnt_staff                     = StaffService::getInstance()->isLnt($before['contract_company_id']);
        $edit_lnt_flag                    = empty($attributes['is_edit_company']) && $is_lnt_staff;
        $changeAtt['contract_company_id'] = CompanyService::getInstance()->getStaffContractCompany($staffInfoId,
            !empty($changeAtt['node_department_id']) ? $changeAtt['node_department_id'] : $baseStaff->node_department_id,
            $edit_lnt_flag);

        $trans = BaseStaffInfo::getDb()->beginTransaction();
        if (!empty($changeAtt)) {
            $baseStaff->setAttributes($changeAtt);
            $attrRes = $baseStaff->update(true, array_keys($changeAtt));
            if ($attrRes === false) {
                $trans->rollback();
                $error = $baseStaff->getFirstErrors();
                return [key($error), reset($error)];
            }

            //企业邮箱状态 1=已开启 2=已注销关闭 0=待开启 3 待注销
            if(!empty($changeAtt['email'])) {
                $email = HrEmails::find()->where(['staff_info_id' => $staffInfoId])->one();
                if(!$email) {
                    $email = new HrEmails();
                    $email->staff_info_id = $staffInfoId;
                    $email->state = 0;
                    $email->email = $changeAtt['email'];

                } else {
                    $email->email = $changeAtt['email'];
                }
                if (!$email->save()) {
                    $trans->rollback();
                    return ['company email save fail!'];
                }
            }
        }

        if (!empty($changeItems)) {
            if (($itemRes = StaffItems::setItems($staffInfoId, $changeItems)) && $itemRes === false) {
                $trans->rollback();
                return ['fail'];
            }
        }

        if (is_array($changePositions) && count($changePositions) > 0) {
            //角色配置
            if(self::setPositionCategory($staffInfoId, $changePositions) === false) {
                Yii::$app->logger->write_log(sprintf('setPositionCategory staff_info_id: %d,positions: %s', $staffInfoId, json_encode($changePositions)), 'info');
                $trans->rollback();
                return ['fail'];
            }
        }

        $newStaff = self::view($staffInfoId);
        $newStaff['reset_password'] = $reset_password;//是否需要重置密码

        if (!empty($face_blacklist_identity)) {
            $newStaff['face_blacklist_identity'] = $face_blacklist_identity;//命中黑名单证件号
        }

        if($newStaff['formal'] == 0) {
            $newStaff['outsourcing_category'] = $newStaff['outsourcing_type'] == 'individual' ? 1 : 2;//外协员工类型
        } else {
            $newStaff['outsourcing_category'] = 0;//非外协传0
        }

        $operaterId = !empty($operaterId) ? $operaterId : 1000;

        $newStaff['operator_id'] = $operaterId;

        //如果有name,需要同步过去
        $changeAtt = array_merge($changeAtt, $changeItems);
        if (count(array_intersect(['email', 'mobile', 'state', 'sys_department_id', 'node_department_id', 'name', 'mobile_company','CAR_TYPE', 'MANGER', 'sys_store_id', 'company_name_ef'], array_keys($changeAtt)))
            && !StaffSyncService::getInstance()->saveStaffInfo($newStaff, 1)) {
            Yii::$app->logger->write_log(['update_ms' => $changeAtt], 'info');
            $trans->rollback();
            return ['send msg to ms fail'];
        }

        if(in_array($newStaff['formal'] ,[1,4])){
            // 同步更新附件表数据和状态
            $changeAtt['source'] = 'updateItems';
            $modifyStaffAnnexRet = HrStaffAnnexInfoService::getInstance()->modifyHrStaffAnnexInfo($staffInfoId, $changeAtt);
            if (!$modifyStaffAnnexRet) {
                $trans->rollback();
                return ['modifyHrStaffInfoAnnexInfo fail'];
            }
        }


        $trans->commit();
        $today = gmdate('Y-m-d', time() + TIME_ADD_HOUR * 3600);
        //合同到期日变更 续签了，释放hold  离职的就不会恢复了
        if (isCountry('MY') && !empty($changeAtt['contract_expiry_date'])
            && !empty($before['contract_expiry_date'])
            && strtotime($changeAtt['contract_expiry_date']) != strtotime($before['contract_expiry_date'])
            && strtotime($changeAtt['contract_expiry_date']) >= strtotime($today . ' + 7 days')
            && $baseStaff->state != BaseStaffInfo::STATE_RESIGN
        ) {
            $mq = new RocketMQ('renew-contract-release-hold');
            $mq->sendToMsg([
                'staff_info_id' => $staffInfoId,
                'before_date'   => $before['contract_expiry_date'],
                'after_date'    => $changeAtt['contract_expiry_date'],
                'state'         => empty($changeAtt['state']) ? $baseStaff->state : $changeAtt['state'],
            ]);
        }

        Yii::$app->logger->write_log(['beforeInfo'=>$before,'newInfo'=>$newStaff], 'info');

        if (
            $baseStaff->formal == BaseStaffInfo::FORMAL_YES &&
            $baseStaff->is_sub_staff == BaseStaffInfo::IS_SUB_STAFF_NO &&
            !empty($changeAtt['hire_date']) &&
            substr($before['hire_date'],0,10) != substr($changeAtt['hire_date'],0,10) &&
            (
                (
                    isCountry('PH') &&
                    $baseStaff->hire_type == BaseStaffInfo::HIRE_TYPE_MONTHLY_SALARY
                ) or (
                    isCountry('TH') &&
                    (
                        $baseStaff->hire_type == BaseStaffInfo::HIRE_TYPE_FORMAL || 
                        (in_array($baseStaff->hire_type,[BaseStaffInfo::HIRE_TYPE_DAILY_SALARY,BaseStaffInfo::HIRE_TYPE_HOURLY_WAGE]) && $baseStaff->hire_times >= 365) || 
                        ($baseStaff->hire_type == BaseStaffInfo::HIRE_TYPE_MONTHLY_SALARY && $baseStaff->hire_times >= 12)
                    )
                )
            )
        ) {
            $callbackParams = [
                'staff_info_id' => $baseStaff->staff_info_id,
                'hire_date'     => $changeAtt['hire_date'],
            ];
            Yii::$app->redis->lpush(RedisListKeyEnums::CREATE_HR_PROBATION, json_encode($callbackParams));
        }
        //修改离职日期 同步hcm 计算员工short notice
        if (isCountry('MY') && $baseStaff->is_sub_staff == 0 &&  in_array($baseStaff->formal ,[BaseStaffInfo::FORMAL_YES,StaffInfo::FORMAL_TRAINEE])  && !empty($newStaff['leave_date'])) {
            $mq = new RocketMQ('cal-short-notice');
            $mq->sendToMsg(['staff_info_id' => $staffInfoId], 2);
        }

        //初始化年假记录
        $mq = new RocketMQ('annual-leave-days');
        $mq->sendToMsg(['staff_info_id' => $staffInfoId, 'lang' => 'en'],3);
        Yii::$app->logger->write_log('annual-leave-days ' . $staffInfoId, 'info');

        StaffService::getInstance()->staffStateChange($before,$newStaff,$operaterId);

        if (isset($changeAtt['state'])) {
            if ($changeAtt['state'] == 2 && $before['state'] != 2) {
                //加入黑名单
                self::addStaffBlacklist($staffInfoId, $operaterId,true);
            }elseif ($changeAtt['state'] == BaseStaffInfo::STATE_RESIGN && $before['state'] == BaseStaffInfo::STATE_RESIGN && isset($changeAtt['leave_reason']) &&  $changeAtt['leave_reason'] != $before['leave_reason']){
                //更新黑名单
                self::addStaffBlacklist($staffInfoId, $operaterId,false,$before);
            }

            Yii::$app->logger->write_log('removeBlackGreyList start staff_info_id: ' . $staffInfoId . 'state:' . $changeAtt['state'] . 'before_state:' .$before['state'], 'info');
            if (in_array($changeAtt['state'],[BaseStaffInfo::STATE_ON_JOB,BaseStaffInfo::STATE_SUSPENSION]) && $before['state'] == BaseStaffInfo::STATE_RESIGN){
                // 作废 连续旷工三天以上 的黑灰名单
                StaffService::getInstance()->removeBlackGreyList($before,$operaterId,$newStaff);
            }
            //停职离职发送消息
            self::courierStaffLeavelsendMessage($staffInfoId);

            $dept_detail = SysDepartment::find()
                ->select(['id', 'ancestry', 'ancestry_v2', 'name', 'manager_id', 'manager_name', 'company_id'])
                ->where(['deleted' => 0])
                ->andWhere(['id' => $baseStaff->node_department_id])
                ->asArray()
                ->one(Yii::$app->get('r_backyard'));

            if($changeAtt['state'] == 2 && $before['state'] != 2) {

                //离职工号发送mq, 判断是否是同步工号，是同步工号,则不发it工单
                $res = StaffService::getInstance()->getSyncStaffInfo($staffInfoId);
                if(!$res) {
                    self::sendStaffLeavelMQ(['staff_info_id' => $staffInfoId, 'leave_date' => $changeAtt['leave_date']]);
                }
                if(YII_COUNTRY == 'TH' && !empty($dept_detail) && in_array($dept_detail['company_id'], [ '70001'])) {
                    //- 当员工HCM所属部门为：E-Commerce SAAS Sales[865]及下级：
                    $e_commerce_ids = SysGroupDeptV2::getMyDeptList(865);
                    if(!in_array($baseStaff->node_department_id, $e_commerce_ids)) {
                        $body = [
                            'staff_info_id' => $staffInfoId,
                            'state' => $changeAtt['state'],
                            'leave_date' => $changeAtt['leave_date'] ?? '',
                            'stop_duties_date' => '',
                            'company_id' => $dept_detail['company_id'] ?? '',
                            'sys_store_name' => $attr['sys_store_name'] ?? ''
                        ];
                        Yii::$app->hris_to_flash_hr_mns->syncStaffState($body);
                    }
                }

                if(in_array(YII_COUNTRY, ['TH', 'PH', 'MY'])) {
                    //菲律宾 马来 员工离职判断如果是组织负责人 更换直线上级
                    (new StaffManager())->updateStaffLeaveManager($staffInfoId, $operaterId, $manager_store_list);
                }

	            //发送离职邮件多国家离职信息同步
	            //https://l8bx01gcjr.feishu.cn/docs/doccnPlJmiRSig08LWcKFGVkdzo#
	            //$is_ms =1 代表 非 ms 操作离职  需要发送邮件
	            if($is_ms == 1 ) {
		            self::ResignationNoticeEmail($staffInfoId);
	            }
            }

            if($changeAtt['state'] == 3) {
                if(YII_COUNTRY == 'TH' && !empty($dept_detail) && in_array($dept_detail['company_id'], ['70001'])) {
                    //- 当员工HCM所属部门为：E-Commerce SAAS Sales[865]及下级：
                    $e_commerce_ids = SysGroupDeptV2::getMyDeptList(865);
                    if(!in_array($baseStaff->node_department_id, $e_commerce_ids)) {
                        $body = [
                            'staff_info_id' => $baseStaff->staff_info_id,
                            'state' => $changeAtt['state'],
                            'leave_date' => '',
                            'stop_duties_date' => $changeAtt['stop_duties_date'] ?? '',
                            'company_id' => $dept_detail['company_id'] ?? ''
                        ];
                        Yii::$app->hris_to_flash_hr_mns->syncStaffState($body);
                    }
                }
            }
        }
        if(in_array($baseStaff->formal, [1,4]) && $baseStaff->is_sub_staff == 0) {

            // 变更离职log表逻辑
            if (
                isset($changeAtt['state']) &&
                $changeAtt['state'] == BaseStaffInfo::STATE_RESIGN &&
                $before['state'] == BaseStaffInfo::STATE_RESIGN &&
                (
                    (isset($changeAtt['leave_reason']) && $changeAtt['leave_reason'] != $before['leave_reason']) ||
                    (isset($changeAtt['leave_date']) && date('Y-m-d', strtotime($changeAtt['leave_date'])) != date('Y-m-d', strtotime($before['leave_date'])))
                )
            ){
                LeaveManageService::getInstance()->updateLeaveManageLog([
                    'staff_info_id'              => $staffInfoId,
                    'leave_date'                 => $changeAtt['leave_date'] ?? $baseStaff->leave_date,
                    'leave_reason'               => $changeAtt['leave_reason'] ?? $baseStaff->leave_reason,
                    'leave_type'                 => $changeAtt['leave_type'] ?? $baseStaff->leave_type,
                    'leave_source'               => $changeAtt['leave_source'] ?? $baseStaff->leave_source,
                    'operate_id'                 => $operaterId,
                ]);
            }

            //同步leave_manager
            if ((isset($changeAtt['state']) && $changeAtt['state'] == 2 && $before['state'] != 2)
                ||
                (isset($changeAtt['wait_leave_state']) && $changeAtt['wait_leave_state'] == 1 && $before['wait_leave_state'] != 1)
                || !empty($flash_hr_apply_resign_time)
            ) {

                $leave_source = $changeAtt['leave_source'] ?? $baseStaff->leave_source;
                $leave_type = $changeAtt['leave_type'] ?? $baseStaff->leave_type;
                $leave_reason = $changeAtt['leave_reason'] ?? $baseStaff->leave_reason;
                $leave_date = $changeAtt['leave_date'] ?? $baseStaff->leave_date;
                LeaveManageService::getInstance()->updateLeaveManage([
                    'staff_info_id'              => $staffInfoId,
                    'leave_date'                 => $leave_date,
                    'leave_source'               => $leave_source,
                    'leave_type'                 => $leave_type,
                    'leave_reason'               => $leave_reason,
                    'operate_id'                 => $operaterId,
                    'state'                      => $changeAtt['state'],
                    'before_wait_leave_state'    => $before['wait_leave_state'],
                    'before_state'               => $before['state'],
                    'flash_hr_apply_resign_time' => $flash_hr_apply_resign_time,
                ]);

                //同步新离职资产 员工离职信息
                StaffService::getInstance()->pushRedisSyncResignAssetsStaff([
                    'event_type' => 'staff_resign',
                    'params'     => [
                        'staff_info_id'      => $staffInfoId,
                        'source'             => $leave_source,
                        'operation_staff_id' => $operaterId,
                        'staff_state'        => $changeAtt['state'] ?? $baseStaff->state,
                        'wait_leave_state'   => $changeAtt['wait_leave_state'] ?? $baseStaff->wait_leave_state,
                        'leave_date'         => $leave_date,
                        'last_work_date'     => date('Y-m-d', strtotime($leave_date)-86400)
                    ],
                ]);
            }

            if (
                (isset($changeAtt['state']) && $changeAtt['state'] == BaseStaffInfo::STATE_ON_JOB && $before['state'] == BaseStaffInfo::STATE_RESIGN)
                ||
                (isset($changeAtt['wait_leave_state']) && $before['state'] == BaseStaffInfo::STATE_ON_JOB && $newStaff['state'] == BaseStaffInfo::STATE_ON_JOB && $changeAtt['wait_leave_state'] == BaseStaffInfo::WAIT_LEAVE_STATE_NO && $before['wait_leave_state'] == BaseStaffInfo::WAIT_LEAVE_STATE_YES)
                ||
                ($before['state'] == BaseStaffInfo::STATE_SUSPENSION &&  $newStaff['state'] == BaseStaffInfo::STATE_ON_JOB &&  $newStaff['wait_leave_state'] == BaseStaffInfo::WAIT_LEAVE_STATE_NO )
            ) {
                //原来是离职 或者原来是待离职 变成在职 同步新离职资产 员工在职信息
                StaffService::getInstance()->pushRedisSyncResignAssetsStaff([
                    'event_type' => 'staff_work',
                    'params'     => [
                        'staff_info_id'      => $staffInfoId,
                        'staff_name'         => $baseStaff->name,
                        'operation_staff_id' => $operaterId,
                        'cancel_reason'      => $attributes['cancel_reason'] ?? '',
                    ],
                ]);
            }

            //离职、停职变为在职(非待离职) 或 待离职到非待离职(在职)
            if (
                (
                    (
                        isset($changeAtt['state']) &&
                        $changeAtt['state'] == BaseStaffInfo::STATE_ON_JOB &&
                        $newStaff['wait_leave_state'] == BaseStaffInfo::WAIT_LEAVE_STATE_NO &&
                        $before['state'] != BaseStaffInfo::STATE_ON_JOB
                    ) ||
                    (
                        isset($changeAtt['wait_leave_state']) &&
                        $before['state'] == BaseStaffInfo::STATE_ON_JOB &&
                        $newStaff['state'] == BaseStaffInfo::STATE_ON_JOB &&
                        $before['wait_leave_state'] == BaseStaffInfo::WAIT_LEAVE_STATE_YES &&
                        $changeAtt['wait_leave_state'] == BaseStaffInfo::WAIT_LEAVE_STATE_NO
                    )
                ) && self::$handle_hold
            ) {
                $params = [
                    'staff_info_id' => $staffInfoId,                                //员工id
                    'hold_reason'   => 'incomplete_resignation_procedures',         //hold原因(BY申请离职)
                    'hold_source'   => 0,                                           //来源->全部来源都要处理
                    'handle_hold'   => 1,                                           //处理hold
                    'operator_id'   => $operaterId,                                 //操作人
                ];
                Yii::$app->jrpc->staff_release_hold($params);
            }

            //当员工管理修改员工将在职状态改为“在职”，自动将「hold员工管理」的hold来源是「BY申请离职」的hold原因为「离职手续不全」的工资hold和提成hold状态，变为“已处理”
            $wait_leave_state = isset($changeAtt['wait_leave_state']) ? $changeAtt['wait_leave_state'] : $before['wait_leave_state'];
            $staff_state = isset($changeAtt['state']) ? $changeAtt['state'] : $before['state'];
            if((isset($changeAtt['state']) && $changeAtt['state'] == BaseStaffInfo::STATE_ON_JOB && $before['state'] != BaseStaffInfo::STATE_ON_JOB && $wait_leave_state == BaseStaffInfo::WAIT_LEAVE_STATE_NO)
            || (isset($changeAtt['wait_leave_state']) && $staff_state == BaseStaffInfo::STATE_ON_JOB && $before['wait_leave_state'] == BaseStaffInfo::WAIT_LEAVE_STATE_YES && $changeAtt['wait_leave_state'] == BaseStaffInfo::WAIT_LEAVE_STATE_NO)
            ) {
                StaffService::getInstance()->pushSyncBackyardSourceHoldHandle([
                    'staff_info_id' => $staffInfoId,
                    'language' => Yii::$app->language
                ]);
            }

            //网点变更 更新支援信息
            if(isset($changeAtt['sys_store_id']) && $before['sys_store_id'] != $changeAtt['sys_store_id'] && $changeAtt['sys_store_id'] != '-1') {
                $params = [
                    'staff_info_id' => $staffInfoId,
                    'store_id' => $changeAtt['sys_store_id'],
                ];
                StaffSupportService::getInstance()->pushUpdateStaffSupport($params);
            }

            $old_node_department_id = $baseStaff->node_department_id;
            if (isset($changeAtt['node_department_id']) && $changeAtt['node_department_id'] != $old_node_department_id) {
                //cfo部门及子部门
                $cfo_department_ids = SysGroupDeptV2::getMyDeptList(333);
                if (in_array($old_node_department_id, $cfo_department_ids)) {
                    $api_param = [
                        'department_id' => $old_node_department_id,
                        'staff_info_id' => $staffInfoId,
                        'operator_id'   => $operaterId,
                    ];
                    Yii::$app->logger->write_log('staff_info_financial: ' . json_encode($api_param), 'info');

                    Yii::$app->jrpc->sync_update_organization_cfo_department($api_param, 'en');
                }


            }
            //非新建员工 变更后职位是网点主管 并且 原网点和变更后网点不一致
            //非新建员工 变更前职位不等于变更后职位 并且 变更后职位是网点主管
            //非新建员工 在职状态在职 并且 原在职状态是离职状态 并且职位是网点主管

            $supervisorJobTitleId = StaffService::getInstance()->store_only_one_job_title;


            $staff_job_title = isset($changeAtt['job_title']) ? $changeAtt['job_title'] : $baseStaff->job_title;
            if ((isset($changeAtt['sys_store_id']) && $before['sys_store_id'] != $changeAtt['sys_store_id'] && in_array($staff_job_title, $supervisorJobTitleId))
                || (isset($changeAtt['job_title']) && $before['job_title'] != $changeAtt['job_title'] && in_array($changeAtt['job_title'], $supervisorJobTitleId))
                || (isset($changeAtt['state']) && $changeAtt['state'] == BaseStaffInfo::STATE_ON_JOB && $before['state'] == BaseStaffInfo::STATE_RESIGN && in_array($staff_job_title, $supervisorJobTitleId))
            ) {
                //网点主管 变更网点 或者 职位变动成网点主管
                $sys_store_id = isset($changeAtt['sys_store_id']) ? $changeAtt['sys_store_id'] : $baseStaff->sys_store_id;
                StaffService::getInstance()->pushChangeStoreSupervisorManager([
                    'store_id' => $sys_store_id,
                    'staff_info_id' => $staffInfoId,
                    'operator_id' => $operaterId,
                    'manager_position_state' => in_array(YII_COUNTRY, ['TH', 'PH', 'MY']) ? BySysStore::MANAGER_POSITION_STATE_YES : null
                ]);
            }

            //变为停职，停职记录
            if (isset($changeAtt['state']) && $changeAtt['state'] == BaseStaffInfo::STATE_SUSPENSION) {
                $origin_info['state'] = $before['state'];
                $origin_info['wait_leave_state'] = $before['wait_leave_state'];
                $origin_info['leave_date'] = $before['leave_date'];
                $suspensionData = [
                    'staff_info_id'    => $staffInfoId,
                    'stop_duties_date' => $changeAtt['stop_duties_date'],
                    'stop_duty_reason' => $changeAtt['stop_duty_reason'],
                    'hire_type'        => $changeAtt['hire_type'] ?? $baseStaff->hire_type,
                    'operate_id'       => $operaterId,
                    'origin_info'      => json_encode($origin_info, JSON_UNESCAPED_UNICODE),
                ];
                if($before['state'] != BaseStaffInfo::STATE_SUSPENSION) {
                    StaffService::getInstance()->addSuspensionManageLog($suspensionData);
                } else {
                    StaffService::getInstance()->updateSuspensionManageLog($suspensionData);
                }
            }

            //hrbp 角色变更 oa 人才盘点菜单变更
            if(in_array(68, $before['position_category']) && isset($attributes['position_category']) &&  !in_array(68, $attributes['position_category'])) {
                //角色变更后 没有hrbp角色 删除oa 人才盘点菜单
                Yii::$app->jrpc->sync_update_hrbp_talent_review_permissions($staffInfoId, 0);
            }
            if(!in_array(68, $before['position_category']) && in_array(68, $changePositions)) {
                //角色变更后 新增hrbp 角色 增加oa 人才盘点菜单
                Yii::$app->jrpc->sync_update_hrbp_talent_review_permissions($staffInfoId, 1);
            }
            //hrbp、Hr Service角色表更
            StaffService::getInstance()->syncApprovalJurisdictionChanges($before['position_category'], $newStaff['position_category'], $staffInfoId, $operaterId);


            if (isCountry('PH') &&
                (
                    empty($changeAtt['email'])
                    || (!empty($changeAtt['sys_store_id']) && $changeAtt['sys_store_id'] != $before['sys_store_id'])
                    || (!empty($changeAtt['job_title']) && $changeAtt['job_title'] != $before['job_title'])
                    || (!empty($changePositions) && (array_diff($before['position_category'], $changePositions) || array_diff($changePositions, $before['position_category'])))
                )
            ) {
                $checkParams['staff_info_id'] = $staffInfoId;
                $checkParams['sys_store_id']  = !empty($changeAtt['sys_store_id']) ? $changeAtt['sys_store_id'] : $before['sys_store_id'];
                $checkParams['job_title']     = !empty($changeAtt['job_title']) ? $changeAtt['job_title'] : $before['job_title'];
                $checkParams['role_id']       = !empty($changePositions) ? $changePositions : $before['position_category'];
                $checkResult                  = StaffService::getInstance()->checkCompanyEmail($checkParams);
                Yii::$app->logger->write_log(['auto_create_email_updateItems' => ['params' => $checkParams, 'res' => $checkResult]], 'info');


                if (!empty($checkResult['email'])) {
                    //写入库，发送邮箱时使用。
                    $autoCreateEmail['staff_info_id'] = $staffInfoId;
                    $autoCreateEmail['date']          = gmdate('Y-m-d', time() + TIME_ADD_HOUR * 3600);
                    $autoCreateEmail['email']         = $checkResult['email'];
                    StaffService::getInstance()->autoCreateEmail($autoCreateEmail);

                    $messageBody = [
                        'staff_info_id' => $staffInfoId,
                        'email'         => $checkResult['email'],
                    ];

                    $rmq                       = new RocketMQ('update-staff-info');
                    $sendData['jsonCondition'] = json_encode($messageBody, JSON_UNESCAPED_UNICODE);
                    $sendData['handleType']    = RocketMQ::TAG_HR_STAFF_UPDATE;
                    $rmq->setShardingKey($staffInfoId);
                    $result = $rmq->sendOrderlyMsg($sendData, 5);

                    Yii::$app->logger->write_log([$messageBody, $result], 'info');
                }
            }

        }



        if (empty($attrRes) && empty($itemRes)) {
            return true;
        }

        if(self::$update_items_is_save_operate_log) {
            // 默认的日志类型是 staff
            $operateType = Enums::$operateLogTypeMap[self::$update_items_operate_type];
            //hold 停职任务 子账号场景要增加子账号操作记录显示
            if(!empty($attributes['log_sub_staff'])){
                $newStaff['sub_account'] = $attributes['log_sub_staff'];
            }
            $log = new HrOperateLogs();
            $log->operater = $operaterId;
            $log->type = $operateType;
            $log->staff_info_id = $staffInfoId;
            $log->after = json_encode(['body' => $newStaff]);
            if ($before ?? false) {
                $log->before = json_encode(['body' => $before]);
            }
            $log->request_body =  molten_get_traceid();
            $log_result = $log->save();
            if(!$log_result) {
                Yii::$app->logger->write_log([
                    'error_message' => $log->getErrors(),
                    'function' => 'updateItems',
                    'staff_info_id' => $staffInfoId,
                    'attributes' => $attributes,
                    'operaterId' => $operaterId,
                    'reset_password' => $reset_password,
                    's_ms' => $is_ms
                ]);
            }
        }

        //同步变更直线上级
        if(count($before) > 0 && $baseStaff->is_sub_staff == 0) {
            $before_manager = $before['manager'] ?? '';
            $after_manager = $newStaff['manager'] ?? '';

            if($before_manager != $after_manager) {
                //Yii::$app->jrpc->staffChangeManager($staffInfoId,$before_manager,$after_manager);
                //直线上级变更同步离职资产
                StaffService::getInstance()->pushRedisSyncResignAssetsStaff([
                    'event_type' => 'change_manager',
                    'params'     => [
                        'staff_info_id'      => $staffInfoId,
                        'before_manager_id'  => $before_manager,
                        'after_manager_id'   => $after_manager,
                        'operation_staff_id' => $operaterId,
                    ],
                ]);
                // 上级信息确认变更时，放入变更的数组中
                self::$change_leader_list[$after_manager][] = $staffInfoId;
            }
        }
        if (isset($attributes['hire_date']) && in_array($baseStaff->formal,[1,4])) {
            // 同步更新winhr电子合同里面的入职日期
            Staff::updateContractWinHr($staffInfoId,$attributes['hire_date']);
        }

        $before_week_working_day = $before['week_working_day'] ?? 0;
        $before_rest_type = $before['rest_type'] ?? 0;
        $before_working_day_rest_type = $before_week_working_day . $before_rest_type;
        if(isset($changeAtt['week_working_day']) && isset($changeAtt['rest_type'])) {
            $after_working_day_rest_type = $changeAtt['week_working_day'] . $changeAtt['rest_type'];
            if($before_working_day_rest_type != $after_working_day_rest_type) {
                //通知轮休
                $params = [
                    'staff_info_id' => $staffInfoId,
                    'date_at' => gmdate('Y-m-d', time() + TIME_ADD_HOUR * 3600),
                    'remark' => $before_working_day_rest_type . '->' . $after_working_day_rest_type
                ];
                Yii::$app->jrpc->changeWorkingDayRestDay($params);
            }
        }
        //更新排班建议
        $updateSchedulingSuggestNumber = false;
        if(isset($changeAtt['week_working_day']) && isset($changeAtt['rest_type'])) {
            $after_working_day_rest_type = $changeAtt['week_working_day'] . $changeAtt['rest_type'];
            if($before_working_day_rest_type != $after_working_day_rest_type) {
                $updateSchedulingSuggestNumber = true;
            }
        }
        if (isset($changeAtt['state']) && $changeAtt['state']!= $before['state']
            || isset($changeAtt['job_title']) && $changeAtt['job_title']!= $before['job_title']
            || isset($changeAtt['sys_store_id']) && $changeAtt['sys_store_id']!= $before['sys_store_id']
                ) {
            $updateSchedulingSuggestNumber = true;
        }
        if ($updateSchedulingSuggestNumber) {
            $storeIds = isset($changeAtt['sys_store_id']) && $changeAtt['sys_store_id']!= $before['sys_store_id'] ? [$before['sys_store_id'],$changeAtt['sys_store_id']] : [$before['sys_store_id']];
            Yii::$app->jrpc->updateSchedulingSuggestNumber(['store_ids' => $storeIds]);
        }
        //员工在职状态变化进MQ
        if (in_array($changeAtt['formal'] ?? $baseStaff->formal,
                [BaseStaffInfo::FORMAL_YES, BaseStaffInfo::FORMAL_TRAINEE])
            && ($changeAtt['is_sub_staff'] ?? $baseStaff->is_sub_staff) == BaseStaffInfo::WAIT_LEAVE_STATE_NO) {
            $onJobChangeBefore = [
                'state'            => $before['state'],
                'wait_leave_state' => $before['wait_leave_state'],
                'leave_date'       => $before['leave_date'],
                'leave_source' => $before['leave_source'],
                'leave_type' => $before['leave_type'],
                'leave_reason' => $before['leave_reason'],
                'hire_date' => $before['hire_date'],
                'hire_type' => $before['hire_type'],
                'stop_duties_date' => $before['stop_duties_date'],
                'stop_duty_reason' => $before['stop_duty_reason'],
                'job_title_grade_v2' => $before['job_title_grade_v2'],
                'operate_id' => $operaterId,
            ];
            $onJobChangeAfter  = [
                'state'            => $baseStaff->state,
                'wait_leave_state' => $baseStaff->wait_leave_state,
                'leave_date'       => $baseStaff->leave_date,
                'leave_source' => $baseStaff->leave_source,
                'leave_type' => $baseStaff->leave_type,
                'leave_reason' => $baseStaff->leave_reason,
                'hire_date' => $baseStaff->hire_date,
                'hire_type' => $baseStaff->hire_type,
                'stop_duties_date' => $baseStaff->stop_duties_date,
                'stop_duty_reason' => $baseStaff->stop_duty_reason,
                'job_title_grade_v2' => $baseStaff->job_title_grade_v2,
                'operate_id' => $operaterId,
            ];
            StaffSyncService::getInstance()->producerStaffOnJobChange($baseStaff->staff_info_id, $onJobChangeBefore,
                $onJobChangeAfter);
        }
        return true;
    }

    public static function getStoreInfo($storeId) {
        $result = SysStore_fle::find()->select(['id','name','short_name','detail_address','manager_id','manager_name','manager_phone','lat','lng','province_code','city_code','district_code','sorting_no','country_code'])->where(['id' => $storeId])->asArray()->one();
        return $result;
    }

    //win_hr入职员工 同步薪资构成
    public static function updateSalaryWinHr($staff_info_id,$entry_id) {
        $query = Yii::$app->get('backyard')
                        ->createCommand("SELECT hr_entry.entry_id,offer.money,offer.exp,offer.food,offer.position,offer.rental,offer.fuel from hr_entry LEFT JOIN hr_interview_offer offer on hr_entry.interview_offer_id=offer.id where hr_entry.entry_id=:entry_id")
                        ->bindValue(':entry_id', $entry_id)
                        ->queryOne();
        if($query) {
            $salary_temp = new HrStaffSalary();
            $before = $salary_temp->getAttributes();//原始值
            $salary_temp->staff_info_id = $staff_info_id;
            $salary_temp->base_salary = $query['money'];
            $salary_temp->exp_allowance = $query['exp'];
            $salary_temp->position_allowance = $query['position'];
            $salary_temp->food_allowance = $query['food'];
            $salary_temp->car_rental = $query['rental'];
            $salary_temp->trip_payment = $query['fuel'];
            if($salary_temp->save() === false) {
                Yii::$app->logger->write_log('win_hr入职员工 同步薪资构成失败，可能出现的原因'.json_encode($salary_temp->getErrors(), JSON_UNESCAPED_UNICODE));

            } else {
                $after = $salary_temp->getAttributes();//变更后的值
                $log = new HrOperateLogs();
                $log->operater = '-1';
                $log->before = json_encode(['body' => $before]);
                $log->after = json_encode(['body' => $after]);
                $log->type = 'salary';
                $log->staff_info_id = $staff_info_id;
                $r = $log->save();
                if (!$r) {
                    Yii::$app->logger->write_log('win_hr入职员工 日志增加失败，可能出现的原因'.json_encode($log->getErrors(), JSON_UNESCAPED_UNICODE));
                }
            }
        } else {
            Yii::$app->logger->write_log('未找到入职员工信息；'.$staff_info_id.'not find；entry_id：'.$entry_id);
        }
    }

    //win_hr入职员工 同步电子合同入职日期
    public static function updateContractWinHr($staff_info_id,$entry_date) {
        try {
            $r = HrStaffContract::updateAll(['entry_date' => $entry_date],['staff_id' => $staff_info_id]);
            //$sql = "update `hr_staff_contract` set `entry_date`='".$entry_date."' where `staff_id` ='".$staff_info_id."'";
            /*
            if (!$r){
                Yii::$app->logger->write_log('修改hr_staff_contract表失败，工号：'.$staff_info_id.'entry_date：'.$entry_date);
            }else{
                Yii::$app->logger->write_log('修改hr_staff_contract表成功，工号：'.$staff_info_id.'entry_date：'.$entry_date,'info');
            }
            */
            Yii::$app->logger->write_log(['message' => 'update_hr_staff_contract', 'staff_info_id' => $staff_info_id, 'entry_date' => $entry_date, 'result' => $r], 'info');
        } catch (\Exception $e) {
            Yii::$app->logger->write_log(['message' => $e->getMessage() , 'file' => $e->getMessage(), 'line' => $e->getMessage(), 'staff_info_id' => $staff_info_id, 'entry_date' => $entry_date]);
        }
    }

    // 更新员工个人邮箱
    public static function updateStaffPersonalEmail($staff_info_id, $personal_email, $operaterId) {
        $baseStaff = BaseStaffInfo::find()->where(['staff_info_id' => $staff_info_id])->one();
        if (!$baseStaff) {
            return ['not found staff'];
        }

        $before = self::view($staff_info_id);
        $changeAtt = [
            'personal_email' => $personal_email
        ];

        $baseStaff->setAttributes($changeAtt);
        $attrRes = $baseStaff->update(true, array_keys($changeAtt));
        if ($attrRes === false) {
            return ['update error'];
        }

        $newStaff = self::view($staff_info_id);
        $log = new HrOperateLogs();
        $log->operater = $staff_info_id;
        //如果操作人不为空，则修改操作人
        if(!empty($operaterId)){
            $log->operater = $operaterId;
        }
        $log->type = 'staff';
        $log->staff_info_id = $staff_info_id;
        $log->after = json_encode(['body' => $newStaff]);
        if ($before ?? false) {
            $log->before = json_encode(['body' => $before]);
        }
        $log->save();
        return true;

    }

    //更新员工待离职状态
    public static function updateStaffWaitLeave($staff_info_id, $param, $operaterId = '', $project_source = '',$staff_resign_id = 0, $work_handover = 0) {

        $baseStaff = BaseStaffInfo::find()->where(['staff_info_id' => $staff_info_id])->one();
        if (!$baseStaff) {
            return ['not found staff'];
        }

        //员工状态已经是待离职状态
        if($baseStaff->wait_leave_state == 1) {
            //return ['员工状态已经是待离职状态'];
        }

        //员工停职 离职状态的话 不能变更待离职状态
        $_from = $param['from'] ?? '';
        unset($param['from']);
        if (!empty($_from) && in_array($_from ,['suspend_work','company_termination_contract'])){
            if($baseStaff->state == 2) {
                return ['该员工是离职状态，不能修改成待离职状态'];
            }
        }else{
            if(in_array($baseStaff->state,[2,3])) {
                return [Yii::$app->lang->get('staff_state_error_1')];
            }
        }


        //if(!in_array($param['leave_reason'],[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 99])) {
        $name_list = [];
        array_map(function($value) use (&$name_list){
            $name_list = array_merge($name_list, array_values($value));
        }, Yii::$app->sysconfig->leave_type_reasson);
        if(!in_array($baseStaff->hire_type,StaffInfo::$agentTypeTogether)  && !in_array($param['leave_reason'],$name_list)) {
            return ["leave_reason in(". implode(',', $name_list) .")"];
        }


        $before = self::view($staff_info_id);
        $changeAtt = [];
        foreach (['leave_date','leave_reason','wait_leave_state','payment_state','payment_markup','stop_payment_type','leave_source','leave_reason_remark','leave_type'] as $attr) {
            if (!empty($param[$attr])) {
                $changeAtt[$attr] = trim($param[$attr]);
            }
        }

        if (empty($changeAtt)) {
            return ['not need update'];
        }
        $changeAtt['is_history_leave'] = 1;
        $changeAtt['state'] = 1;
        $baseStaff->setAttributes($changeAtt);
        $attrRes = $baseStaff->update(true, array_keys($changeAtt));
        if ($attrRes === false) {
            return ['update error'];
        }
        $newStaff = self::view($staff_info_id);
        $log = new HrOperateLogs();
        $log->operater = $staff_info_id;
        //如果操作人不为空，则修改操作人
        if(!empty($operaterId)){
            $log->operater = $operaterId;
        }
        $log->type = 'staff';
        $log->staff_info_id = $staff_info_id;
        $log->after = json_encode(['body' => $newStaff]);
        if ($before ?? false) {
            $log->before = json_encode(['body' => $before]);
        }
        $log->save();

        //进入leave_manger
        $leave_source = isset($changeAtt['leave_source']) ? $changeAtt['leave_source'] : $baseStaff->leave_source;
        $leave_type = isset($changeAtt['leave_type']) ? $changeAtt['leave_type'] : $baseStaff->leave_type;
        $leave_reason = isset($changeAtt['leave_reason']) ? $changeAtt['leave_reason'] : $baseStaff->leave_reason;
        $leave_date = isset($changeAtt['leave_date']) ? $changeAtt['leave_date'] : $baseStaff->leave_date;

        LeaveManageService::getInstance()->updateLeaveManage([
            'staff_info_id'           => $staff_info_id,
            'leave_date'              => $leave_date,
            'leave_source'            => $leave_source,
            'leave_type'              => $leave_type,
            'leave_reason'            => $leave_reason,
            'operate_id'              => !empty($operaterId) ? $operaterId : $staff_info_id,
            'project_source'          => $project_source,
            'sate'                    => $baseStaff->state,
            'before_wait_leave_state' => $baseStaff->wait_leave_state,
            'before_state'            => $baseStaff->state,
        ]);

        //同步新离职资产 员工离职信息
        StaffService::getInstance()->pushRedisSyncResignAssetsStaff([
            'event_type' => 'staff_resign',
            'params'     => [
                'staff_info_id'      => $staff_info_id,
                'source'             => $leave_source,
                'operation_staff_id' => !empty($operaterId) ? $operaterId : $staff_info_id,
                'staff_state'        => $baseStaff->state,
                'wait_leave_state'   => $changeAtt['wait_leave_state'],
                'leave_date'         => $leave_date,
                'last_work_date'     => date('Y-m-d', strtotime($leave_date) - 86400), //最后工作日，离职前一天
                'staff_resign_id'    => $staff_resign_id,
                'work_handover'      => $work_handover,
            ],
        ]);
        //同步ms
        StaffSyncService::getInstance()->saveStaffInfo($newStaff, 1);

        //员工在职状态变化进MQ
        if ($before && in_array($before['formal'],
                [BaseStaffInfo::FORMAL_YES, BaseStaffInfo::FORMAL_TRAINEE])
            && $before['is_sub_staff'] == BaseStaffInfo::WAIT_LEAVE_STATE_NO) {
            $onJobChangeBefore = [
                'state'            => $before['state'],
                'wait_leave_state' => $before['wait_leave_state'],
                'leave_date'       => $before['leave_date'],
                'leave_source' => $before['leave_source'],
                'leave_type' => $before['leave_type'],
                'leave_reason' => $before['leave_reason'],
                'hire_date' => $before['hire_date'],
                'hire_type' => $before['hire_type'],
                'stop_duties_date' => $before['stop_duties_date'],
                'stop_duty_reason' => $before['stop_duty_reason'],
                'job_title_grade_v2' => $before['job_title_grade_v2'],
                'operate_id' => $operaterId,
            ];
            $onJobChangeAfter  = [
                'state'            => $baseStaff->state,
                'wait_leave_state' => $baseStaff->wait_leave_state,
                'leave_date'       => $baseStaff->leave_date,
                'leave_source' => $baseStaff->leave_source,
                'leave_type' => $baseStaff->leave_type,
                'leave_reason' => $baseStaff->leave_reason,
                'hire_date' => $baseStaff->hire_date,
                'hire_type' => $baseStaff->hire_type,
                'stop_duties_date' => $baseStaff->stop_duties_date,
                'stop_duty_reason' => $baseStaff->stop_duty_reason,
                'job_title_grade_v2' => $baseStaff->job_title_grade_v2,
                'operate_id' => $operaterId,
            ];
            StaffSyncService::getInstance()->producerStaffOnJobChange($baseStaff->staff_info_id, $onJobChangeBefore,
                $onJobChangeAfter);
        }
        return true;
    }

    //员工hold 管理
    public static function updateStaffHoldFromBiHold($staff_info_id, $param, $operaterId = '-1') {

        try {
            $baseStaff = BaseStaffInfo::find()->where(['staff_info_id' => $staff_info_id])->one();
            if (!$baseStaff) {
                return ['not found staff'];
            }

            if(!in_array($param['type'],[1, 2])) {
                return ['type error'];
            }

            $base_stop_payment_type = array_filter(explode(',',$baseStaff->stop_payment_type));//工资阻止发放类型 1工资 2 提成
            $base_payment_markup = array_filter(explode(',',$baseStaff->payment_markup));//工资阻止发放原因
            $param_stop_payment_type = explode(',',$param['stop_payment_type']);
            $param_payment_markup = explode(',',$param['payment_markup']);
            $attributes = [];
            if($param['type'] == 1) {
                //hold
                $base_payment_markup = array_diff($base_payment_markup, ["101", "102"]);
                $stop_payment_type = array_unique(array_merge($base_stop_payment_type,$param_stop_payment_type));
                $payment_markup = array_unique(array_merge($base_payment_markup,$param_payment_markup));
                $attributes = [
                    'payment_state' => 2,//工资发放状态
                    'stop_payment_type' => implode(',',$stop_payment_type),//工资阻止发放类型
                    'payment_markup' => implode(',',$payment_markup) //工资阻止发放原因
                ];
            }

            if($param['type'] == 2) {
                //释放
                $stop_payment_type = array_diff($base_stop_payment_type,$param_stop_payment_type);//工资阻止发放类型 1工资 2提成
                if(count($stop_payment_type) == 0) {
                    if(!in_array($param['payment_markup'],['101','102'])) {
                        return ['payment markup error'];
                    }
                    $attributes['payment_state'] = 1;
                    $attributes['payment_markup'] = $param['payment_markup'];
                }
                $attributes['stop_payment_type'] = implode(',',$stop_payment_type);//工资阻止发放类型
            }

            if(count($attributes) == 0) {
                return ['empty param'];
            }
            //批量释放hold 导致redis带宽报警，故改用直接查员工表 不能改成从库查询
            $before = StaffInfo::find()->where(['staff_info_id' => $staff_info_id])->asArray()->one();
            $baseStaff->setAttributes($attributes);
            $attrRes = $baseStaff->update(true, array_keys($attributes));
            if ($attrRes === false) {
                return ['update error'];
            }
            $newStaff = StaffInfo::find()->where(['staff_info_id' => $staff_info_id])->asArray()->one();
            if(!empty($param['sub_staff_info_id'])){
                $newStaff['sub_account'] = $param['sub_staff_info_id'];
            }

            $log = new HrOperateLogs();
            $log->operater = $operaterId;
            $log->type = 'staff';
            $log->staff_info_id = $staff_info_id;
            $log->after = json_encode(['body' => $newStaff]);
            if ($before ?? false) {
                $log->before = json_encode(['body' => $before]);
            }
            $log->save();
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('updateStaffHoldFromBiHold，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';工号:'.$staff_info_id.'参数:'.json_encode($param));
            return false;
        }
    }

    /**
     * //泰国菲律宾特殊逻辑, Network Management部门员工，离职类型为辞退（不赔偿），并且离职原因为“连续旷工”的，不进入黑名单
     * @param $staffInfo
     * @return bool
     * @throws InvalidConfigException
     */
    protected static function countryDepartmentIsAddBlacklist($staffInfo): bool
    {

        if(YII_COUNTRY == 'TH' && $staffInfo['sys_department_id'] == SysDepartmentEnums::NETWORK_MANAGEMENT_TH){
            return true;
        }

        if ((YII_COUNTRY == 'MY' && in_array($staffInfo['sys_department_id'],
                    [SysDepartmentEnums::HUB_MANAGEMENT_MY, SysDepartmentEnums::NETWORK_MANAGEMENT_MY])) ||
            (YII_COUNTRY == 'PH' && in_array($staffInfo['sys_department_id'],
                    [SysDepartmentEnums::NETWORK_MANAGEMENT_PH, SysDepartmentEnums::HUB_MANAGEMENT_PH]))
        ) {
            $jobDepartmentRelation = HrJobDepartmentRelation::find()->select('position_type')->where(['department_id' => $staffInfo['node_department_id']])->andWhere(['job_id' => $staffInfo['job_title']])->one(Yii::$app->get('r_backyard'));
            return $jobDepartmentRelation && $jobDepartmentRelation->position_type == HrJobDepartmentRelation::POSITION_TYPE_FRONT_LINER;
        }
        return false;
    }


    //加入黑名单
    public static function addStaffBlacklist($staff_info_id,$fbid,$is_add = true,$before = []) {
        try{
            $model = Staff::view($staff_info_id);
            if(empty($model)) {
                return;
            }

            Yii::$app->logger->write_log('addDelinquencyList start staff_info_id:'.$staff_info_id.',state:'.$model['state'] .' is_sub_staff '.$model['is_sub_staff'], 'info');
            if (YII_COUNTRY == 'TH' && $model['state'] == BaseStaffInfo::STATE_RESIGN){
                if (
                    $model['is_sub_staff'] == 1 ||
                    !in_array($model['formal'],[BaseStaffInfo::FORMAL_YES,BaseStaffInfo::FORMAL_TRAINEE])
                ) {
                    return;
                }
                //泰国走新的逻辑
                if (!$is_add){
                    // 先删除原有的 次数和原因 黑名单
                    StaffService::getInstance()->delDelinquencyList($model,$fbid,$before);
                }
                // 添加
                StaffService::getInstance()->addDelinquencyList($model,$fbid,$is_add,$model);
                return;
            }

            $diffDate = 0;
            if (isset($model['leave_date']) && isset($model['hire_date'])){
                $diffDate = (new \DateTime($model['leave_date']))->diff((new \DateTime($model['hire_date'])))->format('%a');
            }

            //是否走添加黑名单逻辑
            $isAddBlack = false;

            //离职类型为自愿离职，离职时工作天数在90天以内,TH/PH/MY去掉该逻辑
            if (
                !in_array(YII_COUNTRY, ['TH', 'MY', 'PH'])
                && $model['state'] == BaseStaffInfo::STATE_RESIGN
                && $model['leave_type'] == BaseStaffInfo::LEAVE_TYPE_ACTIVE
                && $diffDate < 90
            ) {
                $isAddBlack = true;
            }

            //辞退不赔偿/辞退赔偿,进入黑名单，不区分国家
            if (
                $model['state'] == BaseStaffInfo::STATE_RESIGN
                && in_array($model['leave_type'], [BaseStaffInfo::LEAVE_TYPE_COMPENSATE, BaseStaffInfo::LEAVE_TYPE_NO_COMPENSATE])
            ) {
                $isAddBlack = true;
            }

            //泰国菲律宾特殊逻辑, Network Management部门员工，离职类型为辞退（不赔偿），并且离职原因为“连续旷工”的，不进入黑名单
            if (self::countryDepartmentIsAddBlacklist($model)) {
                if (
                    $model['state'] == BaseStaffInfo::STATE_RESIGN
                    && $model['leave_type'] == BaseStaffInfo::LEAVE_TYPE_NO_COMPENSATE
                    && $model['leave_reason'] == BaseStaffInfo::LEAVE_REASON_SKIP_WORK
                ) {
                    $isAddBlack = false;
                }
            }

            //if ($model['state'] == 2 && (($model['leave_reason'] ==1 && $diffDate<90) || $model['leave_reason'] ==2 || in_array($model['leave_type'],[1, 2, 3]))) {
            if ($isAddBlack) {
                $_leave_date = isset($model['leave_date'])?date("Y-m-d",strtotime($model['leave_date'])):'';
                $_hire_date = isset($model['hire_date'])?date("Y-m-d",strtotime($model['hire_date'])):'';

                $url = Yii::$app->params['blacklist'];
                $identity = isset($model['identity'])?$model['identity']:'';
                $name = isset($model['name'])?$model['name']:'';
                $mobile = isset($model['mobile'])?$model['mobile']:'';
                $remark = "";
                /*
                if ($model['leave_reason'] == 1){
                    $type = 3;
                    $remark = "เคยร่วมงานกับบริษัทฯไม่ครบ 90 วัน(วันที่เริ่มงาน:".$_hire_date." วันที่ลาออก:".$_leave_date.")";
                }elseif ($model['leave_reason'] == 2){
                    $type = 2;
                    $remark = "วันที่เริ่มงาน:".$_hire_date." วันที่ลาออก:".$_leave_date;
                }else{
                    $type = 0;
                }
                */

                switch ($model['leave_type']) {
                    case 1:
                        //1自愿离职
                        $type = 7;
                        break;
                    case 2:
                        //2辞退不赔偿
                        $type = 8;
                        break;
                    case 3:
                        //3辞退赔偿
                        $type = 9;
                        break;
                    default:
                        $type = 0;
                }
                if (in_array(YII_COUNTRY,['VN','ID','MY','PH'])) {

                    //ToDo 这里不要改语言 否则会导致黑名单移除存在问题
                    $remark = Yii::$app->lang->get('hire_date','','en').':'.date('Y-m-d', strtotime($model['hire_date']))
                        .' / '.Yii::$app->lang->get('leave_date','','en').':'.date('Y-m-d', strtotime($model['leave_date']))
                        .' / '.Yii::$app->lang->get('leave_source','','en').':'.Yii::$app->lang->get('leave_source_'.$model['leave_source'],'','en')
                        .' / '.Yii::$app->lang->get('leave_reason','','en').':'.Yii::$app->lang->get('leave_reason_'.$model['leave_reason'],'','en');
                } else {

                    $remark = Yii::$app->lang->get('hire_date','','th').':'.date('Y-m-d', strtotime($model['hire_date']))
                        .' / '.Yii::$app->lang->get('leave_date','','th').':'.date('Y-m-d', strtotime($model['leave_date']))
                        .' / '.Yii::$app->lang->get('leave_source','','th').':'.Yii::$app->lang->get('leave_source_'.$model['leave_source'],'','th')
                        .' / '.Yii::$app->lang->get('leave_reason','','th').':'.Yii::$app->lang->get('leave_reason_'.$model['leave_reason'],'','th');
                }
                $post_data = [
                    "identity" => $identity,
                    "name" => $name,
                    "mobile" => $mobile,
                    "type" => $type,
                    "submitter_staff_id" => "$fbid",
                    "remark" => $remark,
                ];
                if(empty($fbid) || $fbid == '-1') {
                    unset($post_data['submitter_staff_id']);
                }

                $data = Yii::$app->http->post($url,$post_data);
                $data = json_decode($data, true);
                Yii::$app->logger->write_log(['blacklist' => $post_data, 'result' => $data], 'info');

                if (isset($data['code']) && $data['code'] == 1){
                    Yii::$app->logger->write_log('blacklist : 黑名单添加成功，id '.$data['data']['id'].' 工号 '.$staff_info_id.' 操作人 '.$fbid,'info');
                }else{
                    $errmsg = isset($data['msg'])?$data['msg']:'';
                    if(strrpos($errmsg, "not need") !== false){
                        return;
                    }
                    Yii::$app->logger->write_log('blacklist : 黑名单添加失败,msg '.$errmsg.' 工号 '.$staff_info_id.' identity '.$identity.'操作人 '.$fbid);
                }
            }
        }catch (\Exception $e){
            Yii::$app->logger->write_log('加入黑名单失败，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';工号:'.$staff_info_id);
        }
    }

    //该员工是片区负责人，变更成可以离职并给网点经理和网点主管发消息
    public static function courierStaffLeavelsendMessage($staff_info_id) {
        try {
            $model = Staff::view($staff_info_id);
            if(empty($model)) {
                return;
            }
            if(in_array($model['state'], [2, 3])) {
                // 该员工是片区负责人，变更成可以离职并给网点经理和网点主管发消息
                if (SysDistrict::find()->where(['courier_id' => $model['staff_info_id'], 'deleted' => 0])->exists()) {
                    //查找网点经理和网点主管 发送消息
                    $manager = Staff::getStoreManager($model['sys_store_id']);//网点经理id
                    $supervisor = Staff::getStoreSupervisor($model['sys_store_id']);//网点主管id
                    $jobTitle = HrJobTitle::find()->where(['id' => $model['job_title']])->asArray()->one();//取得职位名称
                    $store = SysStore::find()->where(['id' => $model['sys_store_id']])->asArray()->one();//取得网点名称

                    $leveltime = (new \DateTime('now', new \DateTimeZone(TIMEZONE)))->format('Y-m-d H:i:s');
                    //离职消息内容
                    $msg = "พนักงาน: ".$model['staff_info_id']."  ชื่อ: ".$model['name']."  ตำแหน่ง: ".$jobTitle['job_name']."   สาขา: ".$store['name']."  ลาออกตั้งแต่วันที่ ".$leveltime." ระบบได้ยกเลิกพื้นที่บริการที่เชื่อมกับคูเรียร์ดังกล่าวโดยอัตโนมัติ เพื่อไม่ให้ส่งผลต่อการรับพัสดุ กรุณาเลือกคูเรียร์ที่รับผิดชอบคนใหม่สำหรับพื้นที่บริการนี้";
                    //state=2 离职 state=3 停职
                    if($model['state'] == 3) {
                        //停职消息内容
                        $msg = "พนักงาน: ".$model['staff_info_id']."  ชื่อ: ".$model['name']."  ตำแหน่ง: ".$jobTitle['job_name']."   สาขา: ".$store['name']."  พักงานตั้งแต่วันที่ ".$leveltime." ระบบได้ยกเลิกพื้นที่บริการที่เชื่อมกับคูเรียร์ดังกล่าวโดยอัตโนมัติ เพื่อไม่ให้ส่งผลต่อการรับพัสดุ กรุณาเลือกคูเรียร์ที่รับผิดชอบคนใหม่สำหรับพื้นที่บริการนี้";
                    }

                    if(count($manager) > 0) {
                        //给网点经理发消息
                        Yii::$app->jrpc->backYardMessage($manager['staff_info_id'], $msg, 'แจ้งเตือน');
                    }
                    if(count($supervisor) > 0) {
                        //网点主管发消息
                        Yii::$app->jrpc->backYardMessage($supervisor['staff_info_id'], $msg, 'แจ้งเตือน');
                    }
                    Yii::$app->logger->write_log('该员工是片区负责人离职并给网点经理和网点主管发消息'.$staff_info_id,'info');
                }
            }
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('该员工是片区负责人离职并给网点经理和网点主管发消息失败，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';工号:'.$staff_info_id);
        }
    }

    //角色配置
    public static function setPositionCategory($staff_info_id,$position_category)
    {
        try {
            if (false === HrStaffInfoPosition::deleteAll(['staff_info_id' => $staff_info_id])) {
                return false;
            }

            foreach ($position_category as $role) {
                // 处理 MS 空角色特殊处理
                $new = new HrStaffInfoPosition();
                $new->staff_info_id = $staff_info_id;
                $new->position_category = $role;
                if (!$new->save()) {
                    return false;
                }
            }
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('setPositionCategory，工号：'.$staff_info_id.'角色id：'.json_encode($position_category).',可能出现的问题' . $e->getMessage().';行号:'.$e->getLine());
            return false;
        }
    }

    //职级变更记录
    public static function setJobGradeChangeLog($staff_info_id, $operator_id, $before, $after, $job_grade_change_source = 3)
    {
        $changeLogs = HrJobGradeChange::find()->where(['staff_info_id' => $staff_info_id])->asArray()->all();
        if (empty($changeLogs)) {
            $lastEffectiveDate = is_array($before) ? $before['hire_date']: $before->hire_date;
        } else {
            $effectiveDateList = array_column($changeLogs, 'change_date');
            $lastEffectiveDate = max($effectiveDateList);
        }

        $new = new HrJobGradeChange();
        $new->staff_info_id = $staff_info_id;
        $new->data_source = $job_grade_change_source; //员工管理
        $new->before_job_title_grade = is_array($before) ? $before['job_title_grade_v2']: $before->job_title_grade_v2;
        $new->after_job_title_grade = $after->job_title_grade_v2;
        $new->before_department_id = is_array($before) ? $before['node_department_id']: $before->node_department_id;
        $new->after_department_id = $after->node_department_id;
        $new->before_job_title_id = is_array($before) ? $before['job_title']: $before->job_title;
        $new->after_job_title_id = $after->job_title;

        $jobGrade = is_array($before) ? $before['job_title_grade_v2']: $before->job_title_grade_v2;
        $new->grade_change_reason = $jobGrade > $after->job_title_grade_v2 ? 2: 1;
        $new->last_change_date = $lastEffectiveDate;
        $new->change_date = $after->job_grade_effective_date;
        $new->days = self::calcDays($lastEffectiveDate, $after->job_grade_effective_date);
        $new->operator_id = $operator_id;
        if (!$new->save()) {
            return false;
        }
    }

    private static function calcDays($before_date, $after_date)
    {
        if (strtotime($before_date) > strtotime($after_date)) {
            return 0;
        }
        // 创建两个日期
        $date1 = new DateTime($before_date);
        $date2 = new DateTime($after_date);

        // 计算差异
        $interval = $date1->diff($date2);
        return $interval->days ?? 0;
    }

    //员工转岗
    public static function staffChangeJob($staff_info_id, $param, $operater) {
        try {

            $before = self::view($staff_info_id) ?? [];
            if(empty($before)) {
                return ['not found staff'];
            }
            $model = self::combination($before);
            $model->creater = $operater ?? -1;


            if(empty($param['department_id'])) {
                return ['code' => 0, 'msg' => ['not found department']];
            }
            //不验证
            $model->superJobTransfer = $param['super_job_transfer'] ?? 0;
            $model->superJobTransferNotJump = $param['super_job_transfer_not_jump'] ?? 1;


            $model->node_department_id = $param['department_id'];
            $model->sys_department_id = (string)SysGroupDeptV2::SearchSysDeptId($param['department_id']);
            $is_lnt_staff = StaffService::getInstance()->isLnt($before['contract_company_id']);
            $model->contract_company_id = CompanyService::getInstance()->getStaffContractCompany($staff_info_id, $param['department_id'],$is_lnt_staff);
            $model->job_title = $param['job_title'];
            $model->sys_store_id = $param['sys_store_id'];
            $model->positionCategory = $param['position_category'];
            $model->directManager = $param['direct_manager'];

            // 快递员判断
            if (in_array(1, $model->positionCategory ?? []) && !empty($param['car_type'])) {
                $model->newCarType = $param['car_type'];
            }

            if (YII_COUNTRY == 'MY' && in_array($model->job_title, [110, 1199]) && !empty($param['vehicle_type_category'])) {
                $model->vehicleTypeCategory = $param['vehicle_type_category'];
            }

            if(in_array($param['job_title'], VehicleInfoService::getInstance()->getVehicleTypeJobTitle())) {
                $t = VehicleInfo::getVehicleTypeByJobTitle($param['job_title']);
                $model->newCarType = VehicleInfo::$staffCarTypeMap[$t];
                $model->vehicle_source   = intval($param['vehicle_source'] ?? 0);
                $model->vehicle_use_date = $param['vehicle_use_date'] ?? null;
                $model->projectNum = $param['project_num'] ? : null;
            }

            //工作天数&轮休规则 working_day_rest_type
            if(!empty($param['working_day_rest_type'])) {
                $working_day_rest_type_arr = str_split($param['working_day_rest_type']);
                $model->week_working_day = $working_day_rest_type_arr[0];
                $model->rest_type = $working_day_rest_type_arr[1];
            }

            //转岗职级变更规则
            //特殊批量转岗：传入指定的职级(与转岗前一致)
            //一线、非一线：通过职位调整职级
            if ($param['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                $model->job_title_grade_v2 = $param['job_title_grade'];
                self::$save_is_update_job_grade = false;
            } else if (in_array($model->job_title, Staff::getFortLinePosition())) {

                $model->job_title_grade_v2 = self::getDefaultJobTitleGrade($model->node_department_id, $model->job_title);
            }

            if ($before['job_title_grade_v2'] != $model->job_title_grade_v2) {
                $model->job_grade_effective_date = gmdate('Y-m-d',time() + TIME_ADD_HOUR * 3600);
            }

            $before = self::view($model->staff_info_id) ?? [];

            if($before['sys_store_id'] != $model->sys_store_id) {
                $validate_code = [
                    'hris_validate_store_delivery_barangay_staff_info', //菲律宾/马来 验证员工已绑定派送码0不验证 1验证
                    'hris_validate_sys_district', //非菲律宾/马来 该员工是片区负责人 0不验证 1验证
                ];
                $setting_env = SettingEnv::find()->where(['code' => $validate_code])->asArray()->indexBy('code')->all();
                $hris_validate_store_delivery_barangay_staff_info = $setting_env['hris_validate_store_delivery_barangay_staff_info']['set_val'] ?? 1;
                $hris_validate_sys_district = $setting_env['hris_validate_sys_district']['set_val'] ?? 1;

                if(in_array(YII_COUNTRY, ['PH', 'MY'])) {
                    //菲律宾马来 所属网点 该员工已绑定派送码，不能变更所属网点，请通知网点主管重新设置派送码设置及快递员绑定
                    if ($hris_validate_store_delivery_barangay_staff_info == 1 && StoreDeliveryBarangayStaffInfo::find()->where(['staff_info_id' => $model->staff_info_id, 'deleted' => 0])->exists()) {
                        return ['code' => 101, 'msg' => ['store_change_tip8']];
                    }
                } else {
                    // 该员工是片区负责人，不能变更所属网点，请通知网点经理重新设置片区负责人
                    if (!isset($param['super_job_transfer']) && $hris_validate_sys_district == 1 && SysDistrict::find()->where(['courier_id' => $model->staff_info_id, 'deleted' => 0])->exists()) {
                        return ['code' => 101, 'msg' => ['store_change_tip5']];
                    }
                }
            }

            //变更雇佣类型
            if (!empty($param['hire_type']) && $before['hire_type'] != $param['hire_type']) {
                $model->hire_type = $param['hire_type'];
            }

            //月薪制变更雇佣期间
            if (in_array($param['hire_type'], [BaseStaffInfo::HIRE_TYPE_MONTHLY_SALARY,
                    BaseStaffInfo::HIRE_TYPE_DAILY_SALARY,
                    BaseStaffInfo::HIRE_TYPE_UN_PAID]) && $before['hire_times'] != $param['hire_times']) {
                $model->hire_times = $param['hire_times'];
            }

            //职级变更来源 1=职级调整 2=转岗 3=职位变更
            if (($res = self::save($model,$operater, false, [], 2)) && $res !== true) {
                return ['code' => 0, 'msg' => $res];
            }

            //转岗后职位是 bike和van  ，Car ,truck 的情况下 车辆信息同步员工车辆管理

            if(!isset($param['super_job_transfer']) && in_array($param['job_title'], VehicleInfoService::getInstance()->getVehicleTypeJobTitle())){
                Staff::oa_by_transfer_sync_vehicle($staff_info_id,$param);
            }

            $after = Staff::view($model->staff_info_id);
            $log = new HrOperateLogs();
            $log->operater = $operater;
            //$log->request_body = !isset($param['super_job_transfer']) ? json_encode(Yii::$app->request->post()):json_encode($param);
            $log->before = json_encode(['body' => $before]);
            $log->after = json_encode(['body' => $after]);
            $log->type = 'staff';
            $log->staff_info_id = $model->staff_info_id;
            $log->save();
            return ['code' => 1, 'msg' => ['success']];
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('staffChangeJob，可能出现的原因' . $e->getMessage().';行号:'.$e->getLine().';工号:'.$staff_info_id.';操作人:'.$operater.';参数:'.json_encode($param));
            return ['code' => 0, 'msg' => ['error']];
        }
    }

    public static function getDepartmentJobTitleRoles($param,$lang = '') {
        $depeartment_id = $param['department_id'];
        $job_title_id = $param['job_title_id'];
        $list = HrJobTitleRole::find()->where(['sys_depeartment_id' => $depeartment_id])->andWhere(['job_title_id' => $job_title_id])->asArray()->all();
        $roles_arr = [];
        foreach ($list as $key => $value) {
            $roles_arr[] = ['role_id' => $value['role_id'],'role_name' => Yii::$app->lang->get('role_'.$value['role_id'],'',$lang)];
        }
        return $roles_arr;
    }


    /**
     * [staffBill 拿到快递员回款明细 ]
     * @return [type] [// 5321 【PHP】 【FBI】 【HRIS】记录员工未回公款情况 - panjia - 2019年8月9日17:59:09]
     * <AUTHOR> <[email address]>
     */
    public static function staffBill($staff_info_id = [])
    {
        // 无数据直接返回
        if( empty($staff_info_id) ) return [];

        /*处理参数*/
        if( count($staff_info_id) == 1 ){
            $staff_info_id_in = " AND `staff_info_id` = '{$staff_info_id[0]}'";
        }elseif( count($staff_info_id) < 2000 ){
            $staff_info_id_in = " AND `staff_info_id` IN (".implode(",", $staff_info_id).") ";
        }else{
            $staff_info_id_in = '';
        }

        // 时间
        $date = " < '".date('Y-m-d', strtotime("+0 days"))."'";

        /*历史快递员回款*/
        $sql = "
                SELECT
                    staff_info_id,
                    count(staff_info_id) num,
                    SUM(receivable_amount)/100 amount
                FROM
                    store_receivable_bill_detail 
                WHERE
                    state = 0
                {$staff_info_id_in}
                AND
                    business_date {$date}
                GROUP BY
                    staff_info_id
            ";

        /*執行sql*/
        $staffBill = Yii::$app->fle_ads->createCommand($sql)->queryAll();

        /*初始化数据*/
        $data = [];

        /*处理数据*/
        if( !empty($staffBill) ){
            $data = [];
            foreach ($staffBill as $v) {
                $data[$v['staff_info_id']] = $v;
            };
        }
        return $data;
    }

    /**
     * [remittanceBill 拿到网点出纳历史回款明细 ]
     * @return [type] [// 5321 【PHP】 【FBI】 【HRIS】记录员工未回公款情况 - panjia - 2019年8月10日16:10:16]
     * <AUTHOR> <[email address]>
     */
    public static function remittanceBill($staff_info_id = [])
    {
        // 无数据直接返回
        if( empty($staff_info_id) ) return [];

        /*处理参数*/
        if( count($staff_info_id) == 1 ){
            $staff_info_id_in = " AND `staff_info_id` = '{$staff_info_id[0]}'";
        }elseif( count($staff_info_id) < 2000 ){
            $staff_info_id_in = " AND `staff_info_id` IN (".implode(",", $staff_info_id).") ";
        }else{
            $staff_info_id_in = '';
        }
        // 时间
        $date = " < '".date('Y-m-d', strtotime("+0 days"))."'";

        /*历史网点出纳回款*/
        $sql = "
                SELECT
                    `info`.`id` staff_info_id,
                    COUNT(`info`.`id`) num,
                    SUM(IF(`bill`.`parcel_state`=0,`bill`.`parcel_amount`,0)+IF(`bill`.`cod_state`=0,`bill`.`cod_amount`,0))/100 amount
                FROM
                    `staff_info_position` position
                JOIN
                    `staff_info` info ON `info`.`id` = `position`.`staff_info_id`
                JOIN
                    `store_remittance_bill` bill ON `bill`.`store_id` = `info`.`organization_id`
                WHERE
                    `bill`.`state` = 0
                {$staff_info_id_in}
                AND
                    `position_category` = 4
                AND
                    `business_date` {$date}
                GROUP BY
                    `info`.`id`
            ";

        /*執行sql*/
        $remittanceBill = Yii::$app->fle->createCommand($sql)->queryAll();

        /*初始化数据*/
        $data = [];

        /*处理数据*/
        if( !empty($remittanceBill) ){
            $data = [];
            foreach ($remittanceBill as $v) {
                $data[$v['staff_info_id']] = $v;
            };
        }
        return $data;
    }

    public static function updateStaffLeaveDate($param) {

        $staff_info_id = $param['staff_info_id'];
        $leave_date = $param['leave_date'];
        $state = $param['state'];
        $operater = $param['operater'] ?? -1;
        if(empty($staff_info_id) || !is_numeric($staff_info_id)) {
            return ['code' => 0,'msg' => 'Staff ID Can not be empty Or error'];
        }

        if(empty($leave_date)) {
            return ['code' => 0,'msg' => 'Leave Date Can not be empty'];
        }

        if(!in_array($state, [1, 2, 3])) {
            return ['code' => 0,'msg' => 'state error'];
        }

        if(empty($operater) || !is_numeric($operater)) {
            return ['code' => 0,'msg' => 'Operater Can not be empty Or error'];
        }

        $attr = [
            'state' => $state,
            'leave_date' => $leave_date
        ];
        $res = self::updateItems($staff_info_id, $attr, $operater);
        if($res !== true) {
            return ['code' => 0,'msg' => $res];
        }
        return ['code' => 1,'msg' => 'success'];
    }

    public static function getNextLevelStaff($staff_info_id) {
        $list = StaffInfo::find()->select(['staff_info_id'])->where(['manger' => $staff_info_id])->asArray()->all();
        $ids = array_column($list,'staff_info_id');
        $ids = array_map('intval', $ids);
        return $ids;
    }

    public static function sendStaffLeavelMQ($staff_info) {
        //IT工单迁移至所有国家，判断泰国逻辑去掉
        /*if(YII_COUNTRY != 'TH') { //非泰国不往MQ写消息
            return true;
        }*/

        Yii::$app->logger->write_log('StaffLeavelQM 消息写入，消息内容：'.json_encode($staff_info), 'info');
        try {
            if(empty($staff_info)) {
                return false;
            }
            $rocket_mq_http_endpoint = Yii::$app->params["rocket_mq_http_endpoint"];
            $rocket_mq_access = Yii::$app->params["rocket_mq_access"];
            $rocket_mq_secret = Yii::$app->params["rocket_mq_secret"];
            $rocket_topic = Yii::$app->params["rocket_mq_staff_leave_topic"];
            $rocket_instance_id = Yii::$app->params["rocket_mq_instance_id"];
            $client = new MQClient($rocket_mq_http_endpoint,$rocket_mq_access,$rocket_mq_secret);
            $producer = $client->getProducer($rocket_instance_id, $rocket_topic);
            $m = base64_encode(json_encode(['data' => $staff_info]));
            $publishMessage = new TopicMessage($m);
            $producer->publishMessage($publishMessage);
            Yii::$app->logger->write_log('StaffLeavelQM 消息写入成功，消息内容：'.json_encode($staff_info), 'info');
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('sendStaffLeavelMQ 异常，消息内容：'.json_encode($staff_info).'-----异常信息：'.$e->getMessage().'---行号'.$e->getLine());
            return false;
        }
    }
    //员工指定字段信息变更推送 fps系统
    public static function sendStaffFpsMQ($staff_info) {
        if(YII_COUNTRY != 'TH') { //非泰国不往MQ写消息
            return true;
        }

        try {
            if(empty($staff_info)) {
                return false;
            }
            $rocket_mq_http_endpoint = Yii::$app->params["rocket_mq_http_endpoint"];
            $rocket_mq_access = Yii::$app->params["rocket_mq_access"];
            $rocket_mq_secret = Yii::$app->params["rocket_mq_secret"];
            $rocket_topic = Yii::$app->params["rocket_mq_staff_fps_topic"];
            $rocket_instance_id = Yii::$app->params["rocket_mq_instance_id"];
            $client = new MQClient($rocket_mq_http_endpoint,$rocket_mq_access,$rocket_mq_secret);
            $producer = $client->getProducer($rocket_instance_id, $rocket_topic);
            $m = base64_encode(json_encode(['data' => $staff_info]));
            $publishMessage = new TopicMessage($m);
            $result = $producer->publishMessage($publishMessage);
            $msg_id = $result->getMessageId() ?? '';
            Yii::$app->logger->write_log("sendStaffFpsMQ 消息写入成功，消息id:{$msg_id}, bodyMD5 is:" . $result->getMessageBodyMD5().",消息内容：".json_encode($staff_info), 'info');
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('sendStaffFpsMQ 异常，消息内容：'.json_encode($staff_info).'-----异常信息：'.$e->getMessage().'---行号'.$e->getLine());
            return false;
        }
    }


    //根据部门职位获取默认职级
    public static function getDefaultJobTitleGrade($dept_id, $job_title_id) {
        $job_title_grade = 0;
        $dept_relation = HrJobDepartmentRelation::find()->where(['department_id' => $dept_id])->andWhere(['job_id' => $job_title_id])->asArray()->one();
        if(!empty($dept_relation) && !empty($dept_relation['job_level'])) {
            $grade_ids = explode(',', $dept_relation['job_level']);
            sort($grade_ids);
            $job_title_grade = intval($grade_ids[0]);
        }
        return $job_title_grade;

    }





    /**
     * winhr 员工入职同步到车辆管理
     * @param $uid
     * @param $form
     */
    public static function winhr_sync_vehicle($uid,$params){
        Yii::$app->logger->write_log('actionCreate-winhr_sync_vehicle('.$uid.'):' . json_encode($params), 'info');
        $model = VehicleInfo::find()->where(['uid' => $uid])->one();
        if(!$model){
            $model = new VehicleInfo();
            $model->uid = $uid;
            $model->vehicle_type = VehicleInfo::getVehicleTypeByJobTitle($params['job_title']);

            $vehicle_source = $params['vehicle_source'] ?? 0;
            if (YII_COUNTRY == "TH" && $params['job_title'] == '1844') {
                $vehicle_source = VehicleInfo::VEHICLE_SOURCE_COMPANY;
            }

            $model->vehicle_source = intval($vehicle_source);//车辆来源：1-个人车辆；2-租用公司车辆; 3-借用车辆
            $model->plate_number = nameSpecialCharsReplace($params['staff_car_no']?? ''); //车牌号
            $model->license_location = $params['place_cards']?? '' ; //上牌地点
            $model->driver_license_number = $params['driver_number']?? '' ; //驾照号码
            $model->driving_licence_img = $params['driving_licence_img']?? '' ; //驾照图片
            $model->vehicle_img = $params['vehicle_img']?? '' ; //车辆图片
            $model->project_num = $params['project_num']?? null; //项目期数

            $model->registration_certificate_img = $params['registration_certificate_img']?? '' ; //车辆登记簿
            if(!empty($params['vehicle_source']) && $params['vehicle_source'] == 2 && isset($params['vehicle_use_date']) && $params['vehicle_use_date']){
                $model->vehicle_start_date = $params['vehicle_use_date'];//用车开始日期
            }
            //驾照号码  驾照图片
            if (YII_COUNTRY == 'PH') {
                $model->oil_type = in_array($params['staff_car_type'] ,['Truck']) ? 2 : 0;//1 汽油 2 柴油
                $model->oil_subsidy_type = in_array($params['staff_car_type'] ,['Van','Truck']) ? 1 : 0; // 油卡补贴方式1:工资卡2:油卡
            }elseif(YII_COUNTRY == 'MY'){
                $model->vehicle_type_category = $params['vehicle_type_category'] ?? 0 ; //车类型
                $model->oil_subsidy_type = ($params['staff_car_type'] == 'Van' || $params['staff_car_type'] == 'Car') ? 2 : 0; //油卡补贴方式2:油卡
            }

            $model->engine_number = $params['car_engine_number'] ?? '';//发动机号
            $formal_data = [
                'license_location'   => $model->license_location,
                'job_title'          => $params['job_title'],
                'plate_number'       => $model->plate_number,
                'vehicle_type'       => $model->vehicle_type,
                'vehicle_source'     => $model->vehicle_source,
                'vehicle_start_date' => $model->vehicle_start_date ?? '',
            ];
            $model->formal_data = json_encode($formal_data);

            $res = $model->save();

            Yii::$app->logger->write_log('actionCreate-winhr_sync_vehicle('.$uid.')执行结果:' . json_encode($res), 'info');
        }

    }

    /**
         * oa/by 转岗 功能同步车辆信息到员工车辆管理
     * @param $uid
     * @param $params
     */
    public static function oa_by_transfer_sync_vehicle($uid,$params)
    {
        Yii::$app->logger->write_log('oa_by_transfer_sync_vehicle('.$uid.'):' . json_encode($params), 'info');

        try {

            $vehicle_model = VehicleInfo::find()->where(['uid' => $uid])->one();
            if ($vehicle_model) {
                $formal_data = $vehicle_model->formal_data ? json_decode($vehicle_model->formal_data,true) : [];
            } else {
               //不存在，插入一条新数据
                $vehicle_model                   = new VehicleInfo();
                $vehicle_model->uid              = $uid;
                $formal_data = [];
            }
            //更新或新增转岗后的如下字段信息
            $vehicle_model->vehicle_type       = VehicleInfo::getVehicleTypeByJobTitle($params['job_title']) ;
            $vehicle_model->vehicle_source     = intval($params['vehicle_source'] ?? 0);                                                                               //车辆来源：1-个人车辆；2-租用公司车辆; 3-借用车辆
            $vehicle_model->vehicle_start_date = $params['vehicle_source'] == BaseStaffInfo::VEHICLE_SOURCE_RENTAL_CODE ? ($params['vehicle_use_date'] ?? null) : null;//用车开始日期
            $vehicle_model->project_num        = $params['vehicle_source'] == BaseStaffInfo::VEHICLE_SOURCE_RENTAL_CODE ? ($params['project_num'] ?? null) : null;

            $formal_data['vehicle_type'] = $vehicle_model->vehicle_type;
            $formal_data['vehicle_source'] = $vehicle_model->vehicle_source;
            $formal_data['vehicle_start_date'] = $vehicle_model->vehicle_start_date ?? '';
            //记录最新 正式版数据
            $vehicle_model->formal_data = json_encode($formal_data);

            if (YII_COUNTRY == 'MY' && in_array($params['job_title'], [110, 1199])) {
                $vehicle_model->vehicle_type_category = $params['vehicle_type_category'];
            }
            $vehicle_model->save();


        } catch (\Exception $e) {
            Yii::$app->logger->write_log('oa_by_transfer_sync_vehicle异常信息：' . $e->getMessage() . '---行号' . $e->getLine());
        }
    }

    //角色变更
    public static function StaffChangRoles($param) {
        try {
            $staff_info_id = $param['staff_info_id'] ?? 0;
            $roles = $param['roles'] ?? [];
            $operater_id = $param['operater_id'] ?? -1;

            $staff_info = self::view($staff_info_id);
            if(empty($staff_info)) {
                return ['staff_no' ,'common_exception'];
            }
            $before = $staff_info;
            $staff_info['position_category'] = $roles;
            $staff_model = self::combination($staff_info);
            $result = self::save($staff_model, $operater_id);
            if($result !== true) {
                return $result;
            } else {
                $after = self::view($staff_info_id);
                $log = new HrOperateLogs();
                $log->operater = $operater_id;
                //$log->request_body = json_encode([]);
                $log->before = json_encode(['body' => $before ?? []]);
                $log->after = json_encode(['body' => $after]);
                $log->type = 'staff';
                $log->staff_info_id = $staff_info_id;
                $log->save();
                return true;
            }
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('StaffChangRoles 异常信息：' . $e->getMessage() . '---行号' . $e->getLine());
            return ['error'];
        }
    }

    public static function ImportStaffInfo($_file, $fbid,$file_name) {
        try {
            if (!$_file) {
                return ['code' => 0, 'msg' => 'File upload failed', 'data' => []];
            }
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            // 读取文件
            //0 工号//1 网点//2 所属部门// 3职位id// 4角色
            $excel_data = $excel->openFile($_file)
                ->openSheet()
                ->setSkipRows(1)
                ->setType([
                    \Vtiful\Kernel\Excel::TYPE_INT,
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                    \Vtiful\Kernel\Excel::TYPE_INT,
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                ])
                ->getSheetData();

            $store_list = SysStoreTemp::temp();
            array_walk_recursive($excel_data, function(&$value) {
                $value = trim($value);
            });
            $result = $excel_return = [];
            Yii::$app->logger->write_log(['ImportStaffInfo'=>$excel_data],'info');
            foreach ($excel_data as $key => $value) {
                $excel_result['staff_info_id'] = $value[0] ?? '';
                $excel_result['sys_store_id'] = $value[1];
                $excel_result['node_department_id'] = $value[2];
                $excel_result['job_title'] = $value[3];
                $excel_result['position_category'] = $value[4];
                if(empty($excel_result['staff_info_id'])) {
                    $excel_result['result'] = 'Employee No. invalid';
                    $result[] = $excel_result;
                    $excel_return[] = [
                        $excel_result['staff_info_id'], $excel_result['sys_store_id'], $excel_result['node_department_id'], $excel_result['job_title'], $excel_result['position_category'], $excel_result['result']
                    ];
                    continue;
                }
                $staff_info_view = self::view($value[0]);
                if(empty($staff_info_view)) {
                    $excel_result['result'] = 'Employee No. invalid';
                    $result[] = $excel_result;
                    $excel_return[] = [
                        $excel_result['staff_info_id'], $excel_result['sys_store_id'], $excel_result['node_department_id'], $excel_result['job_title'], $excel_result['position_category'], $excel_result['result']
                    ];
                    continue;
                }

                $before = $staff_info_view;
                if(!empty($value[1])) {
                    if(!isset($store_list[$value[1]])) {
                        $excel_result['result'] = 'Branch invalid';
                        $result[] = $excel_result;
                        $excel_return[] = [
                            $excel_result['staff_info_id'], $excel_result['sys_store_id'], $excel_result['node_department_id'], $excel_result['job_title'], $excel_result['position_category'], $excel_result['result']
                        ];
                        continue;
                    } else {
                        $staff_info_view['sys_store_id'] = $value[1];
                    }
                }
                if(!empty($value[2])) {
                    $staff_info_view['node_department_id'] = $value[2];
                }
                if(!empty($value[3])) {
                    $staff_info_view['job_title'] = $value[3];
                }
                if(!empty($value[4])) {
                    $staff_info_view['position_category'] = explode(',', $value[4]);
                }
                try{
                    $staff_info_model = self::combination($staff_info_view);
                    $save_result = self::save($staff_info_model, $fbid);
                    if($save_result !== true) {
                        //保存失败
                        $msg = [];
                        if (($save_result[0] ?? false)) {
                            $msg[] = Yii::$app->lang->get($save_result[0]);
                        }
                        if ($save_result[1] ?? false) {
                            $msg[] = Yii::$app->lang->get($save_result[1]);
                        }
                        $excel_result['result'] = implode(',', $msg);
                    } else {
                        $after = self::view($value[0]);
                        $log = new HrOperateLogs();
                        $log->operater =$fbid;
                        //$log->request_body = json_encode(Yii::$app->request->post());
                        $log->before = json_encode(['body' => array_merge($before)]);
                        $log->after = json_encode(['body' => array_merge($after)]);
                        $log->type = 'staff';
                        $log->staff_info_id = $value[0];
                        $log->save();
                        $excel_result['result'] = 'success';
                    }
                }catch (\Exception $e) {
                    $excel_result['result'] = 'staff error';
                }

                $result[] = $excel_result;
                $excel_return[] = [
                    $excel_result['staff_info_id'], $excel_result['sys_store_id'], $excel_result['node_department_id'], $excel_result['job_title'], $excel_result['position_category'], $excel_result['result']
                ];
            }
            $excel_path = excelToFile(['staff_info_id', 'sys_store_id', 'node_department_id', 'job_title', 'position_category', 'result'], $excel_return, (empty($file_name) ? (time() . 'excel.xlsx') : $file_name));
            $ossObject       = 'hris/' . date('Ymd') . '/' . (empty($file_name) ? time() : $file_name);
            $upload_oss_result = Yii::$app->FlashOss->uploadFile($ossObject, $excel_path);
            Yii::$app->logger->write_log(['excel_return' => $excel_return, 'upload_oss_result' => $upload_oss_result],
                'info');
            return ['code' => 1, 'msg' => 'success', 'data' => ['data' => $result, 'url' => $upload_oss_result,'excel_path'=>$ossObject]];

        } catch (\Exception $e) {
            Yii::$app->logger->write_log('ImportStaffDeptJobTitleStore Exception，可能出现的问题:'.$e->getMessage().';行:'.$e->getLine());
            return ['code' => 0, 'msg' => 'error', 'data' => []];
        }
    }

    /**
     * @description:简历完善同步
     * @param $params ['staff_info_id']  用户 id
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/7/29 22:07
     */

    public static function winhr_by_resume_synchronize($params=[]){
        Yii::$app->logger->write_log('winhr_by_resume_synchronize('.$params['staff_info_id'].'):' . json_encode($params), 'info');
        try {
            $before = self::view( $params['staff_info_id']) ?? [];
            if(empty($before)) {
                return ['not found staff'];
            }



	        $items = [
		        'REGISTER_COUNTRY' => $params['register_country'] ?? 1,               //国家  默认泰国, //户口所在国家
		        'REGISTER_PROVINCE' =>$params['register_province'] ?? '',       //户口所在省, //户口所在省
		        'REGISTER_CITY' => $params['register_city'] ?? '', //户口所在市
		        'REGISTER_DISTRICT' => $params['register_district'] ?? '', //户口所在乡
		        'REGISTER_POSTCODES' =>$params['register_postcodes'] ?? '', //户口所在邮编
		        'REGISTER_HOUSE_NUM' => $params['register_house_num'] ?? '', //户口所在门牌号
		        'REGISTER_VILLAGE_NUM' =>  $params['register_village_num'] ?? '', //户口所在村号
		        'REGISTER_VILLAGE' => $params['register_village'] ?? '', //户口所在村
		        'REGISTER_ALLEY' => $params['register_alley'] ?? '' , //户口所在巷
		        'REGISTER_STREET' => $params['register_street'] ?? '', //户口所在街道
		        'DAD_FIRST_NAME' =>  $params['dad_first_name'] ?? '', //父亲名
		        'DAD_LAST_NAME' =>  $params['dad_last_name'] ?? '' , //父亲姓
		        'MUM_FIRST_NAME' => $params['mum_first_name'] ?? '' , //母亲名
		        'MUM_LAST_NAME' => $params['mum_last_name'] ?? '', //母亲姓
		        'GRADUATE_SCHOOL' =>  $params['graduate_school'] ?? '', // 毕业学校
		        'MAJOR' =>  $params['major'] ?? '', //专业
		        'GRADUATE_TIME' => $params['graduate_time'] ? date('Y-m-d',strtotime($params['graduate_time'])): '', //毕业时间
		        'REGISTER_DETAIL_ADDRESS' => $params['register_detail_address'] ?? '', //工作所在国家户口所在地详细地址

	        ];

            if (YII_COUNTRY == 'ID') {
                $items['REGISTER_RW'] = $params['register_rw'] ?? '';
                $items['REGISTER_RT'] = $params['register_rt'] ?? '';
            }

	        $res = StaffItems::setItems($params['staff_info_id'], $items);

	        if ($res !== true) {
		        return ['code' => 0, 'msg' => $res];
	        }
	        $after = Staff::view($params['staff_info_id']);
	        $log = new HrOperateLogs();
	        $log->operater = $params['staff_info_id'] ?? -1;
	        $log->type = 'staff';
	        $log->staff_info_id = $params['staff_info_id'] ?? -1;
	        $log->after = json_encode(['body' => $after]);
	        if ($before ?? false) {
		        $log->before = json_encode(['body' => $before]);
	        }
	        $log->save();
            return ['code' => 1, 'msg' => ['success']];
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('winhr_by_resume_synchronize异常信息：' . $e->getMessage() . '---行号' . $e->getLine());
        }
    }

    public static function getHrJobDepartmentRelationById($dept_id,$job_title_id) {
        $config_list = [];
        $result = HrJobDepartmentRelation::find()->where(['department_id' => $dept_id])->andWhere(['job_id' => $job_title_id])->asArray()->one(Yii::$app->get('r_backyard'));
        if(!empty($result) && !empty($result['job_level'])) {
            $grade_ids = explode(',', $result['job_level']);

            foreach ($grade_ids as $key => $value) {
                $config_list[] = [
                    'id' => $value,
                    'value' => Yii::$app->sysconfig->config_job_title_grade_v2[$value] ?? ''
                ];
            }
        }
        return $config_list;
    }

    public static function filterGradeListByStaff($gradeList, $currentJobGrade, $editPermission)
    {
        if ($editPermission) {
            if ($editPermission == StaffInfo::JOB_GRADE_EDIT_PERMISSION_1) {
                foreach ($gradeList as $k => $item) {
                    if ($item['id'] > $currentJobGrade) {
                        unset($gradeList[$k]);
                    }
                }
            }
            return array_values($gradeList);
        }

        return [];
    }

    /**
     * 获取管辖区域下网点列表 和 部门
     *
     * @param $staffId
     * hrbp  hr_server 角色
     * @return ['stores'=>[1,2],'flash_home_departments'=>[1,2]]
     */
    public static function dominDepartmentsAndStores($staffId, $type = 1)
    {
        $cache_time =  YII_ENV == 'pro' ? 180 : 1;
        //增加 3 分钟缓存
        $result = Yii::$app->cache->getOrSet('Staff:dominDepartmentsAndStores_'.$staffId.'_'.$type,
            function ($cache) use ($staffId, $type) {
                //获取管辖范围
                $relations                = StaffRelationsService::getInstance()->getStaffJurisdiction($staffId, $type);

                $result = [
                    "flash_home_departments" => $relations['departments'] ?? [],
                    "stores"                 => $relations['stores'] ?? [],
                ];
                //判断是否查询网点类型 -- 片区-- 大区
                //网点里有-2 就不用查 大区 片区 网点类型了
                if (in_array(StaffRelationsService::$all_id, $relations['stores'])) {
                    return $result;
                }
                //查找网点类型
                //查找片区的网点
                //查找大区的网点
                if ($relations['regions'] || $relations['pieces'] || $relations['store_categories']) {
                    $sysStore         = SysStore::find()
                        ->select(['id', 'name'])
                        ->andFilterWhere(['manage_region' => $relations['regions']])
                        ->orFilterWhere(['manage_piece' => $relations['pieces']])
                        ->orFilterWhere(['category' => $relations['store_categories']])
                        ->asArray()
                        ->all(Yii::$app->get('r_backyard'));
                    $result['stores'] = array_values(array_unique(
                        array_merge($result['stores'], array_column($sysStore, 'id'))));
                }

                return $result;
            }, $cache_time);

        return $result;
    }
    /**
     * 职级白名单
     * 获取管辖区域下网点列表 和 部门
     *
     * @param $staffId
     * hrbp  hr_server 角色
     */
    public static function gradeWhiteListDepartmentsAndStores($staffId)
    {
        return self::dominDepartmentsAndStores($staffId,StaffRelationsService::TYPE_JOB_GRADE_WHITElIST);
    }

    //同步在职状态
    public static function syncStaffState($staff_info_list) {
        try {
            if(empty($staff_info_list)) {
                return ['code' => 0, 'msg' => 'error', 'data' => []];
            }

            foreach ($staff_info_list as &$staffInfo) {
                if (!in_array($staffInfo['type'], ['1', '2', '3']) || empty($staffInfo['type'])) { //1 在职 2 离职 3 停职
                    $staffInfo['msg'] = 'type error';continue;
                }

                if (empty($staffInfo['staff_info_id'])) {
                    $staffInfo['msg'] = 'miss staff_info_id';continue;
                }

                if (in_array($staffInfo['type'], ['2', '3']) && empty($staffInfo['day'])) {
                    $staffInfo['msg'] = 'miss day';continue;
                }

                $staffId = $staffInfo['staff_info_id'];
                $attr['state'] = $staffInfo['type'];//在职状态
                if ($staffInfo['type'] == 1) {

                } else if ($staffInfo['type'] == 2) {
                    if(empty($staffInfo['leave_reason'])) {
                        $staffInfo['msg'] = 'miss leave_reason';continue;
                    }
                    $attr['leave_date'] = $staffInfo['day'];//离职日期
                    $attr['leave_reason'] = $staffInfo['leave_reason'];//离职原因 1:个人原因 2:公司开除
                } else {
                    $attr['stop_duties_date'] = $staffInfo['day'];//停职日期
                    if (!empty($staffInfo['day']) &&
                        (empty($staffInfo['stop_duty_reason']) || !in_array($staffInfo['stop_duty_reason'], array_keys(SuspendReasonEnums::SUSPEND_TYPE_REASON_MAP)))){ //存在停职日期，停职原因为空或无效原因
                        $staffInfo['msg'] = 'invalid stop_duty_reason';
                        continue;
                    }
                    $attr['stop_duty_reason'] = $staffInfo['stop_duty_reason'];
                }

                $attr['is_auto_system_change'] = 1;//最后员工状态变更是否由系统触发 1 是 0否

                if(isset($staffInfo['leave_source'])){
                    $attr['leave_source'] = $staffInfo['leave_source'];
                    switch ($attr['leave_source']) {
                        case 3:
                            //来源 旷工三天以上
                            $attr['leave_type'] = 2;
                            $attr['leave_reason'] = 21;
                            break;
                        case 4:
                            //来源 未缴纳公款
                            $attr['leave_type'] = 2;
                            $attr['leave_reason'] = 20;
                            break;
                        case 6:
                            //来源 批量导入 根据离职原因查找离职类型
                            if(in_array($attr['leave_reason'],Yii::$app->sysconfig->leave_type_reasson[1])) {
                                $attr['leave_type'] = 1;
                            }
                            if(in_array($attr['leave_reason'],Yii::$app->sysconfig->leave_type_reasson[2])) {
                                $attr['leave_type'] = 2;
                            }
                            if(in_array($attr['leave_reason'],Yii::$app->sysconfig->leave_type_reasson[3])) {
                                $attr['leave_type'] = 3;
                            }
                            if(in_array($attr['leave_reason'],Yii::$app->sysconfig->leave_type_reasson[4])) {
                                $attr['leave_type'] = 4;
                            }
                            break;
                    }

                }
                if(isset($staffInfo['wait_leave_state'])){
                    $attr['wait_leave_state'] = $staffInfo['wait_leave_state'];
                }

                $operator_id = -1;
                if(isset($staffInfo['operator_id'])){
                    $operator_id = $staffInfo['operator_id'];
                }

                //不处理hold---菲律宾因 个人代理 合同 是否续约导致的 恢复在职 不处理hold, 由业务自行处理
                if(isset($staffInfo['handle_hold'])) {
                   self::$handle_hold = $staffInfo['handle_hold'];//true 处理，false 不处理
                }
                if (($r = Staff::updateItems($staffId, $attr, $operator_id)) && $r === true) {
                    $staffInfo['msg'] = 'ok';
                } else {
                    $staffInfo['msg'] = $r;
                }
            }
            Yii::$app->logger->write_log('syncStaffState 结果：'.json_encode($staff_info_list), 'info');
            return ['code' => 1, 'msg' => 'ok', 'data' => $staff_info_list];
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('syncStaffState  异常信息：' . $e->getMessage() . '---行号' . $e->getLine(). ';参数：'.json_encode($staff_info_list));
            return ['code' => 0, 'msg' => 'error', 'data' => []];
        }
    }

    //往kpi同步
    public static function sendStaffToKpiMQ($param) {
        if(YII_COUNTRY != 'TH') { //非泰国不往MQ写消息
            return true;
        }
        Yii::$app->logger->write_log('sendStaffToKpiMQ 消息写入，消息内容：'.json_encode($param), 'info');
        try {
            if(empty($param)) {
                return false;
            }
            $rocket_mq_http_endpoint = Yii::$app->params["rocket_mq_http_endpoint"];
            $rocket_mq_access = Yii::$app->params["rocket_mq_access"];
            $rocket_mq_secret = Yii::$app->params["rocket_mq_secret"];
            $rocket_topic = Yii::$app->params["rocket_mq_staff_kpi_topic"];
            $rocket_instance_id = Yii::$app->params["rocket_mq_instance_id"];
            $client = new MQClient($rocket_mq_http_endpoint,$rocket_mq_access,$rocket_mq_secret);
            $producer = $client->getProducer($rocket_instance_id, $rocket_topic);
            $m = base64_encode(json_encode($param));
            $publishMessage = new TopicMessage($m);
            $producer->publishMessage($publishMessage);
            Yii::$app->logger->write_log('sendStaffToKpiMQ 消息写入成功，消息内容：'.json_encode($param), 'info');
            return true;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('sendStaffToKpiMQ 异常，消息内容：'.json_encode($param).'-----异常信息：'.$e->getMessage().'---行号'.$e->getLine());
            return false;
        }
    }


	/**
	 * @description: 发送离职邮件 -- 多国家离职信息同步
	 *https://l8bx01gcjr.feishu.cn/docs/doccnPlJmiRSig08LWcKFGVkdzo#
	 * @param $staff_info_id
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2022/2/23 11:31
	 *
	 */
	public static function ResignationNoticeEmail($staff_info_id){
		try {
			Yii::$app->logger->write_log('ResignationNoticeEmail=>[系统自动]HCM员工全局离职操作提醒 触发' . $staff_info_id , 'info');
			if (YII_COUNTRY == 'TH') { //泰国不发邮件
				return true;
			}
			//查询用户是否存在
			$model = Staff::view($staff_info_id);
			if($model['state'] != 2) {
				//不是离职
				Yii::$app->logger->write_log('ResignationNoticeEmail=>[系统自动]HCM员工全局离职操作提醒 不是离职 不需要发送邮件' . $staff_info_id , 'info');
				return  false;
			}
			//检查是否为 ms 工号
			if(!self::isSynchronizationStaffInfo($staff_info_id)){
				return false;
			}

            //获取所有离职原因
            $all_leave_type_reason = StaffLeaveReasonService::getInstance()->getStaffLeaveReasonListFromCache();
            $leave_reason_key = '';
            if(isset($all_leave_type_reason[$model['leave_reason']])) {
                $leave_reason_key = $all_leave_type_reason[$model['leave_reason']]['t_key'];
            }

			$body = '<p><strong>[系统自动]HCM员工全局离职操作提醒</strong></p></br>
				<p>1. HCM离职员工工号 : '. $model['staff_info_id'].'</p></br>
				<p>2. HCM离职员工姓名 : '. $model['name'].'</p></br>
				<p>3. HCM离职员工部门 : '.($model['node_department_name'] ?? "").'</p></br>
				<p>4. HCM离职员工职位 : '.($model['job_title_name'] ?? "") .'</p></br>
				<p>5. 离职日期 		: '.($model['leave_date'] ?? "").'</p></br>
				<p>6. 离职类型 		: '.Yii::$app->lang->get("leave_type_".$model['leave_type'],"","zh").'</p></br>
				<p>7. 离职原因 		: '.Yii::$app->lang->get($leave_reason_key,"","zh").' </p></br>
				<p>8. 离职国家 		: '.YII_COUNTRY.'</p></br>
				<p>9. 离职状态变更时间 : '.gmdate('Y-m-d H:i:s',time()+TIME_ADD_HOUR*3600).'</p></br>
				<p><srong>Thanks，</strong></br>';
			//获取 env 里的邮箱
			$emails= [];
			$ManagerLeaveEmail = SettingEnv::find()->where(['code' => 'ResignationNoticeEmail'])->one();
			if (!empty($ManagerLeaveEmail) && $ManagerLeaveEmail->set_val) {
				$emails = explode(',', $ManagerLeaveEmail->set_val);
			}
			if(empty($emails)){
				throw new \Exception('没有配置邮箱==>ResignationNoticeEmail');
			}
			$send_email_result = Yii::$app->mailer->compose()
			                                      ->setSubject('[系统自动]HCM员工全局离职操作提醒 ' . date('ymd') .YII_ENV )
			                                      ->setHtmlBody($body)
			                                      ->setTo($emails)
			                                      ->send();
			if(!$send_email_result){
				Yii::$app->logger->write_log('ResignationNoticeEmail=>[系统自动]HCM员工全局离职操作提醒=> body=>' . $body . ' 结果：' . json_encode($send_email_result));
			}
			Yii::$app->logger->write_log('ResignationNoticeEmail=>[系统自动]HCM员工全局离职操作提醒=> body=>' . $body . ' 结果：' . json_encode($send_email_result), 'info');
			return true;
		}catch (\Exception $e) {
				Yii::$app->logger->write_log('ResignationNoticeEmail ：'.$staff_info_id.'-----异常信息：'.$e->getMessage().'---行号'.$e->getLine());
				return false;
		}


	}


	/**
	 * @description: ms 同步离职 //默认自愿离职
	 *
	 * @param
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2022/2/23 19:28
	 */
	public static function synchronizationResignation($param) {
		try {
            $staff_info_id    = $param['staff_info_id'] ?? 0;
            $leave_date       = $param['leave_date'] ?? '';
            $state            = $param['state'] ?? 0;
            $wait_leave_state = $param['wait_leave_state'] ?? 0;
            $operater         = $param['operater'] ?? -1;
            $leave_reason     = $param['leave_reason'] ?? 1;
            $leave_type       = $param['leave_type'] ?? 1;
            $leave_source     = $param['leave_source'] ?? 10;
			if (empty($staff_info_id) || !is_numeric($staff_info_id)) {
				return ['code' => 0, 'msg' => 'Staff ID Can not be empty Or error'];
			}

			if (empty($leave_date)) {
				return ['code' => 0, 'msg' => 'Leave Date Can not be empty'];
			}

			if (!in_array($state, [1, 2, 3])) {
				return ['code' => 0, 'msg' => 'state error'];
			}

			if (empty($operater) || !is_numeric($operater)) {
				return ['code' => 0, 'msg' => 'Operater Can not be empty Or error'];
			}

            $attr = [
                'state'            => $state,
                'wait_leave_state' => $wait_leave_state,
                'leave_date'       => $leave_date,
                'leave_reason'     => $leave_reason, //自愿离职
                'leave_type'       => $leave_type, //个人离职
                'leave_source'     => $leave_source,
            ];
			//判断是否为同步工号 和工号是否存在
			$staff_info = BaseStaffInfo::find()->where(['staff_info_id' => $staff_info_id])->one();
			if (empty($staff_info)) {
				return ['code' => 0, 'msg' => ' staff_info empty '];
			}

			$res = self::updateItems($staff_info_id, $attr, $operater, false, 2);
			if ($res !== true) {
				return ['code' => 0, 'msg' => $res];
			}
		}catch (\Exception $e) {
				Yii::$app->logger->write_log('synchronizationResignation ：'.$staff_info_id.'-----异常信息：'.$e->getMessage().'---行号'.$e->getLine());
				return ['code' => 0, 'msg' => ' error'];
		}

		return ['code' => 1,'msg' => 'success'];
	}


	/**
	 * @description: 判断是否为 ms 同步工号
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2022/2/23 19:46
	 */

	public static function isSynchronizationStaffInfo($staff_info_id)
	{
		//todo 这里记得要改
		try {
			$staff = StaffInfo_fle::find()->where(['id' => $staff_info_id])->asArray()->one();
			//sync_country_enable == 1 是同步工号
			if (empty($staff) || !isset($staff['sync_country_enable']) || $staff['sync_country_enable'] != 1) {
				Yii::$app->logger->write_log('msSynchronizationResignation=>ms 同步离职  不是同步工号 ' . $staff_info_id, 'info');
				return false;
			}
			Yii::$app->logger->write_log('msSynchronizationResignation=>检查是 ms  同步工号 ' . $staff_info_id, 'info');
			return true;
		} catch (\Exception $e) {
			Yii::$app->logger->write_log('isSynchronizationStaffInfo ：' . $staff_info_id . '-----异常信息：' . $e->getMessage() . '---行号' . $e->getLine());
			return false;
		}
	}

    /**
     * 更新hub外协员工在职状态
     * @param $params
     * @return array
     */
	public static function updateHubOsStaffState($params) {
	    try {
            $operator_id = -1;
            $state = $params['state'];
            $staff_info_id = $params['staff_info_id'];

            $baseStaff = BaseStaffInfo::find()->where(['staff_info_id' => $staff_info_id])->andWhere(['formal' => 0])->andWhere(['staff_type' => 4])->one();
            if (!$baseStaff) {
                return ['not found staff'];
            }
            $attributes['state'] = $state;
            if($state == 1) {
                $attributes['hire_date'] = date('Y-m-d',strtotime($params['hire_date']));//入职日期
            } else {
                $attributes['leave_date'] = date('Y-m-d',strtotime($params['leave_date']));//离职日期
            }

            $result = Staff::updateItems($staff_info_id, $attributes, $operator_id);//变更员工离职状态
            return $result;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log(['params' => $params, "message" => $e->getMessage(), 'line' => $e->getLine(), 'file' => $e->getFile()], 'error');
            return ['code' => 0, 'msg' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 增加员工操作记录
     * @param $params
     * @return bool
     */
	public static function addOperateLogs($params): bool
    {
        try {
            $op = new HrOperateLogs();
            $op->operater = $params['operater']??0;
            $op->type = strval($params['type']);
            //$op->request_body = $params['request_body']??'';
            $op->staff_info_id = $params['staff_info_id'];
            $op->before = $params['before']??'';
            $op->after = $params['after']??'';
            $re = $op->save();
            Yii::$app->logger->write_log('addOperateLogs : ' . json_encode($params,JSON_UNESCAPED_UNICODE) .' res:'.$re , 'info');
            return $re;
        } catch (\Exception $e) {
            Yii::$app->logger->write_log('addOperateLogs Exception ：' . json_encode($params,JSON_UNESCAPED_UNICODE) . '-----异常信息：' . $e->getMessage() . '---行号' . $e->getLine());
            return false;
        }
    }


    public static function is_db_migration()
    {
        $info = SettingEnv::find()->where(['code' => 'db_migration_20220515'])->asArray()->one();
        if (!empty($info)) {
            if ($info['set_val'] == 1) {
                return false;
            }
        }
        return true;
    }

    /**
     * 同步items表数据
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function syncStaffItems($params): array
    {
        $trans = HrStaffItems::getDb()->beginTransaction();

        try {
            $returnData = ['code' => 1, 'msg' => 'success', 'data' => []];

            if(empty($params['staff_info_id'])){
                throw new Exception(' staff_info_id empty !!!') ;
            }

            $baseStaff = BaseStaffInfo::findBySql('select * from hr_staff_info where staff_info_id = :staff_info_id for update' ,['staff_info_id' => $params['staff_info_id']])->one();
            if(!$baseStaff){
                throw new Exception(' staff_info_id not exist !!!') ;
            }
            if(empty($params['items']) || !is_array($params['items'])){
                throw new Exception(' items empty  or not array !!!') ;
            }

            foreach ($params['items'] as $item => $value) {
                //项目期数
                if ($item == 'project_num') {
                    StaffInfo::insertStaffExtendInfo($params['staff_info_id'], ['project_num' => $value]);
                    continue;
                }

                if($item == 'vehicle_source'){
                    $baseStaff->vehicle_source = $value;
                    $baseStaff->save();
                    continue;
                }
                if ($item == 'vehicle_use_date') {
                    $baseStaff->vehicle_use_date = $value;
                    $baseStaff->save();
                    continue;
                }

                $staffItem = HrStaffItems::find()->where(['staff_info_id' => $params['staff_info_id'],'item' => $item])->one();
                if(!$staffItem){
                    $model = new HrStaffItems();
                    $model->staff_info_id = $params['staff_info_id'];
                    $model->item = $item;
                    $model->value = $value?:'';
                    $model->save();
                }else{
                    $staffItem->value = $value?:'';
                    $staffItem->save();
                }
            }

            $trans->commit();
            return $returnData;
        } catch (\Exception $e) {
            $trans->rollback();
            $returnData = ['code' => 0, 'msg' => $e->getMessage(), 'data' => []];
            Yii::$app->logger->write_log('syncStaffItems Exception ：' . json_encode($params,JSON_UNESCAPED_UNICODE) . '-----异常信息：' . $e->getMessage() . '---行号' . $e->getLine());
            return $returnData;
        }
    }

    public static function updateStatus($staffInfos)
    {
        foreach ($staffInfos as &$staffInfo) {
            if (!in_array($staffInfo['type'], ['1', '2', '3']) || empty($staffInfo['type'])) { // 1 在职 2 离职 3 停职
                $staffInfo['msg'] = 'type error';continue;
            }

            if (empty($staffInfo['staff_info_id'])) {
                $staffInfo['msg'] = 'miss staff_info_id';continue;
            }

            if (in_array($staffInfo['type'], ['2', '3']) && empty($staffInfo['day'])) {
                $staffInfo['msg'] = 'miss day';continue;
            }

            $staffId = $staffInfo['staff_info_id'];
            $attr['state'] = $staffInfo['type'];//在职状态
            if ($staffInfo['type'] == 1) {

            } else if ($staffInfo['type'] == 2) {
                if(empty($staffInfo['leave_reason'])) {
                    $staffInfo['msg'] = 'miss leave_reason';continue;
                }
                $attr['leave_date'] = $staffInfo['day'];//离职日期
                $attr['leave_reason'] = $staffInfo['leave_reason'];//离职原因 1:个人原因 2:公司开除
            } else {
                $attr['stop_duties_date'] = $staffInfo['day'];//停职日期
                if (!empty($staffInfo['day']) &&
                    (empty($staffInfo['stop_duty_reason']) || !in_array($staffInfo['stop_duty_reason'], array_keys(SuspendReasonEnums::SUSPEND_TYPE_REASON_MAP)))){ //存在停职日期，停职原因为空或无效原因
                    $staffInfo['msg'] = 'invalid stop_duty_reason';
                    continue;
                }
                $attr['stop_duty_reason'] = $staffInfo['stop_duty_reason'];
            }
            $attr['is_auto_system_change'] = 1;//最后员工状态变更是否由系统触发 1 是 0否
            if(isset($staffInfo['leave_source'])){
                $attr['leave_source'] = $staffInfo['leave_source'];
                switch ($attr['leave_source']) {
                    case 3:
                        //来源 旷工三天以上
                        $attr['leave_type'] = 2;
                        $attr['leave_reason'] = 21;
                        break;
                    case 4:
                        //来源 未缴纳公款
                        $attr['leave_type'] = 2;
                        $attr['leave_reason'] = 20;
                        break;
                    case 6:
                        //来源 批量导入 根据离职原因查找离职类型
                        if(in_array($attr['leave_reason'],Yii::$app->sysconfig->leave_type_reasson[1])) {
                            $attr['leave_type'] = 1;
                        }
                        if(in_array($attr['leave_reason'],Yii::$app->sysconfig->leave_type_reasson[2])) {
                            $attr['leave_type'] = 2;
                        }
                        if(in_array($attr['leave_reason'],Yii::$app->sysconfig->leave_type_reasson[3])) {
                            $attr['leave_type'] = 3;
                        }
                        if(in_array($attr['leave_reason'],Yii::$app->sysconfig->leave_type_reasson[4])) {
                            $attr['leave_type'] = 4;
                        }
                        break;
                }

            }
            //兼容下hcm app/services/LeaveManagerService.php function cancel(
            if (isset($staffInfo['leave_source'], $staffInfo['leave_reason']) && $staffInfo['leave_source'] == BaseStaffINfo::LEAVE_SOURCE_BACKYARD) {
                $attr['leave_reason'] = $staffInfo['leave_reason'];
            }
            if(isset($staffInfo['leave_date'])){
                $attr['leave_date'] = $staffInfo['leave_date'];
            }
            if(isset($staffInfo['wait_leave_state'])){
                $attr['wait_leave_state'] = $staffInfo['wait_leave_state'];
            }

            if (isset($staffInfo['leave_type'])){
                $attr['leave_type'] = $staffInfo['leave_type'];
            }

            $operaterId = -1;
            if(isset($staffInfo['operaterId'])){
                $operaterId=$staffInfo['operaterId'];
            }

            $attr['cancel_reason'] = $staffInfo['cancel_reason'] ?? '';//hcm撤销离职会传撤销原因参数

            if (($r = self::updateItems($staffId, $attr, $operaterId)) && $r === true) {
                $staffInfo['msg'] = 'ok';
            } else {
                $staffInfo['msg'] = $r;
            }
        }

        return $staffInfos;
    }

    public static function syncItem($items, $fbid)
    {
        $staffInfoId = $fbid;
        $attrs = [];
        $empty_field = (isset($items['empty_field']) && !empty($items['empty_field'])) ? $items['empty_field'] : [];

        foreach (['identity', 'mobile', 'bank_no', 'personal_email','bank_type','nick_name','tax_card','bank_branch_name', 'backup_bank_no', 'bank_no_name'] as $attr) {
            if (!empty($items[$attr])) {
                $attrs[$attr] = $items[$attr];
            }

            // 允许为空的字段
            if (in_array($attr,$empty_field)) {
                $attrs[$attr] = "";
                $attrs['empty_field'] = $empty_field;
            }
        }

        if (isset($items['personal_email']) && !$items['personal_email']) {
            $attrs['personal_email'] = '';
        }

        if(isset($items['nick_name']) && !$items['nick_name']) {
            $attrs['nick_name'] = '';
        }

        if(isset($items['identity']) && !empty($items['identity'])) {
            if(YII_COUNTRY == 'TH') {
                if(!preg_match("/(^[A-Za-z0-9]{8,13}$)|(^[A-Za-z0-9]{18}$)/u", $items['identity'])) {
                    return ['identity', 'staff_identity_length_error'];
                }
            }elseif(YII_COUNTRY == 'ID'){
                if(!preg_match("/(^[A-Za-z0-9_\-]{8,16}$)/u", $items['identity'])) {
                    return ['identity', 'id_staff_identity_length_error'];
                }
            }elseif(YII_COUNTRY == 'PH') {
                if(!preg_match("/(^[A-Za-z0-9_\-]{8,30}$)/u", $items['identity'])) {
                    return ['identity', 'PH_staff_identity_length_error_2'];
                }
            }elseif(YII_COUNTRY == 'LA') {
                if(!preg_match("/(^[A-Za-z0-9_\-]{8,15}$)/u", $items['identity'])) {
                    return ['identity', 'LA_staff_identity_length_error_2'];
                }
            } else {
                if(!preg_match("/(^[A-Za-z0-9_\-]{1,30}$)/u", $items['identity'])) {
                    return ['identity', 'staff_identity_length_error_2'];
                }
            }
        }

        if (!empty($attrs['mobile'])) {
            if (strtoupper(YII_COUNTRY) == 'PH') {
                // 菲律宾
                $attrs['mobile'] = str_pad($attrs['mobile'], 11, "0", STR_PAD_LEFT);
            } elseif (strtoupper(YII_COUNTRY) == 'ID') {
                if (strlen($attrs['mobile']) != 13 && strpos($attrs['mobile'], "0") !== 0 ) {
                    $attrs['mobile'] = str_pad($attrs['mobile'], strlen($attrs['mobile'])+1, "0", STR_PAD_LEFT);
                }
            } else if (strtoupper(YII_COUNTRY) == 'LA' || strtoupper(YII_COUNTRY) == 'MY') {
                // 老挝
                if (strpos( $attrs['mobile'],'0') !== 0) {
                    $pad_len = strlen($attrs['mobile']) == 9 ? 10 : 11;
                    $attrs['mobile'] = str_pad($attrs['mobile'], $pad_len, "0", STR_PAD_LEFT);
                }
            }
        }

        if (empty($attrs)) {
            return ['miss params'];
        }

        if (($res = self::updateItems($staffInfoId, $attrs, $staffInfoId)) && $res !== true) {
            return $res;
        }
        Yii::$app->logger->write_log('ActionSyscItem修改工号信息,工号:'.$staffInfoId.';工号信息:'. json_encode($attrs),'info');
        return (int) $staffInfoId;
    }


    /**
     *
     * @param $fbid
     * @return array|int[]
     *
     */
    public static function salary($fbid)
    {
        $items = HrStaffSalary::findOne($fbid);
        if (!$items) {
            return [
                // 'staff_info_id' => $this->fbid,
                'base_salary' => 0,
                'exp_allowance' => 0,
                'position_allowance' => 0,
                'car_rental' => 0,
                'trip_payment' => 0,
                'notebook_rental' => 0,
                'recommended' => 0,
                'food_allowance' => 0,
                // 'total' => 0,
            ];
        }
        $items = $items->getAttributes();
        unset($items['base_salary_bak']);
        unset($items['created_at']);
        unset($items['updated_at']);
        return $items;
    }

    /**
     *
     * @param $form
     * @param $fbid
     * @return array
     *
     */
    public static function create($form, $fbid)
    {
        $validate = Staff::validate($form);//验证表单元素
        if($validate !== true) {
            return [false, $validate];
        }
        Yii::$app->logger->write_log('actionCreate' . json_encode($form, JSON_UNESCAPED_UNICODE), 'info');
        $model = Staff::combination($form);//检查表单元素
        //防止重复提交
        $staff_edit_key = 'HRIS_'.$model->mobile.$model->identity;
        $c = Yii::$app->cache->redis->get($staff_edit_key);
        if(!empty($c)) {
            return [false, ['Please do not submit repeatedly']];
        }
        Yii::$app->cache->redis->setex($staff_edit_key, 30, $staff_edit_key);

        $before = Staff::view($model->staff_info_id) ?? [];
        if (isset($form['contract_expiry_date']) && isset($before['hire_date']) && $form['contract_expiry_date'] < $before['hire_date']) {

            return [false, ['The contract expiration date cannot be earlier than the entry date']];
        }
        if (($res = Staff::save($model, $fbid)) && $res !== true) {
            //防止重复提交
            Yii::$app->cache->redis->del($staff_edit_key);
            return [false, $res];
        }

        //如果是winhr 同步过来的 数据，并且职位是Van 、Bike Car 则同步添加到员工车辆管理
        //兼容winhr生成公号已存在员工管理，导致不是新数据，不能用isNewRecord来判断
        if(isset($form['data_from']) && in_array($form['data_from'],['winhr','backyard'])){
            $vehicleTypeJobTitle =  VehicleInfoService::getInstance()->getVehicleTypeJobTitle();;
            if(isset($form['job_title']) && in_array($form['job_title'],$vehicleTypeJobTitle)){
                Staff::winhr_sync_vehicle($model->staff_info_id,$form);
            }
        }

        $after = Staff::view($model->staff_info_id);
        //删除不必要字段
        unset($after['is_show_project_num'],$after['job_title_is_deleted']);
        $log = new HrOperateLogs();
        $log->operater = $fbid;
        $log->request_body = molten_get_traceid();
        $log->before = json_encode(['body' => $before], JSON_UNESCAPED_UNICODE);
        $log->after = json_encode(['body' => $after], JSON_UNESCAPED_UNICODE);
        $log->type = 'staff';
        $log->staff_info_id = $model->staff_info_id;
        $r = $log->save();
        if (!$r) {
            Yii::$app->logger->write_log('员工信息日志保存失败，可能出现的问题：' . json_encode($log->getErrors(), JSON_UNESCAPED_UNICODE).';工号：'.$model->staff_info_id);
        }

        //值类型1.部门;2.职位;3.所属网点4:角色
        //部门
        if(count($before) > 0) {
            if($before['sys_department_id'] != $after['sys_department_id']) {
                $staff_transfer_log = new HrStaffTransferLog();
                $staff_transfer_log->staff_info_id = $model->staff_info_id;
                $staff_transfer_log->cur_value = $after['sys_department_id'];
                $staff_transfer_log->old_value = $before['sys_department_id'];
                $staff_transfer_log->type = 1;
                $staff_transfer_log->change_date = date('Y-m-d',time());
                $staff_transfer_log->save();
            }
            //职位
            if($before['job_title'] != $after['job_title']) {
                $staff_transfer_log = new HrStaffTransferLog();
                $staff_transfer_log->staff_info_id = $model->staff_info_id;
                $staff_transfer_log->cur_value = $after['job_title'];
                $staff_transfer_log->old_value = $before['job_title'];
                $staff_transfer_log->type = 2;
                $staff_transfer_log->change_date = date('Y-m-d',time());
                $staff_transfer_log->save();

                //如果职位是从van（110）和bike（13）之间变更
                $needVehicleJobTitle =  VehicleInfoService::getInstance()->getVehicleTypeJobTitle();
                if($before['formal'] == 1 && in_array($before['job_title'],$needVehicleJobTitle) && in_array($after['job_title'],$needVehicleJobTitle)){

                    $vehicle_type = VehicleInfo::getVehicleTypeByJobTitle($after['job_title']); // $after['job_title'] == '110' ? 1 : 0; //车辆类型 van=1，bike=0
                    $vehicle_model = VehicleInfo::find()->where(['uid' => $model->staff_info_id])->one();
                    if(!empty($vehicle_model)){
                        //变更车辆管理信息中的车辆类型信息
                        $vehicle_model->vehicle_type = $vehicle_type;
                        $vehicel_formal_data = json_decode($vehicle_model->formal_data,true);
                        $vehicel_formal_data['vehicle_type'] = $vehicle_type;
                        $vehicle_model->formal_data = json_encode($vehicel_formal_data, JSON_UNESCAPED_UNICODE);
                        $vehicle_model->save();
                        //同步items更新车辆类型
                        $model->newCarType = VehicleInfo::$staffCarTypeMap[$vehicle_model->vehicle_type];
                        $model->save(false);
                    }

                }
            }
            //网点
            if($before['sys_store_id'] != $after['sys_store_id']) {
                $staff_transfer_log = new HrStaffTransferLog();
                $staff_transfer_log->staff_info_id = $model->staff_info_id;
                $staff_transfer_log->cur_value = $after['sys_store_id'];
                $staff_transfer_log->old_value = $before['sys_store_id'];
                $staff_transfer_log->type = 3;
                $staff_transfer_log->change_date = date('Y-m-d',time());
                $staff_transfer_log->save();
            }

            $before_position_category = implode(',',$before['position_category']);
            $after_position_category = implode(',',$after['position_category']);
            if($before_position_category != $after_position_category) {
                $staff_transfer_log = new HrStaffTransferLog();
                $staff_transfer_log->staff_info_id = $model->staff_info_id;
                $staff_transfer_log->cur_value = $after_position_category;
                $staff_transfer_log->old_value = $before_position_category;
                $staff_transfer_log->type = 4;
                $staff_transfer_log->change_date = date('Y-m-d',time());
                $staff_transfer_log->save();
            }

            //同步变更直线上级
            $before_manager = $before['manager'] ?? '';
            $after_manager = $after['manager'] ?? '';
            if($before_manager != $after_manager) {
                //Yii::$app->jrpc->staffChangeManager($model->staff_info_id, $before_manager, $after_manager);

                //同步给kpi 直线上级变更
                $param_kpi = [
                    'event_type' => 'manager',
                    'data' => [
                        'staff_info_id' => $model->staff_info_id,
                        'before_manager' => $before_manager,
                        'after_manager' => $after_manager
                    ],
                ];
                Staff::sendStaffToKpiMQ($param_kpi);
            }

        }


        $newStaffInfoId = $model->staff_info_id;
        //存入redis队里用于异步回调
        $callbackParams = AfterEntryService::getInstance()->entryInitData($model,$form)->fire();
        Yii::$app->logger->write_log("创建工号异步回调 ".json_encode($callbackParams), 'info');
        //防止重复提交
        Yii::$app->cache->redis->del($staff_edit_key);

        return [true, $newStaffInfoId];
    }




    public static function jobGradeEditPermission($staffId, $fbid)
    {
        $staffs = StaffInfo::find()->where(['IN', 'staff_info_id', [intval($staffId), intval($fbid)]])->asArray()->indexBy('staff_info_id')->all();

        // 操作人 编辑权限
        $jobGradeEditPermission = $staffs && isset($staffs[$fbid]) ? $staffs[$fbid]['job_grade_edit_permission'] : StaffInfo::JOB_GRADE_EDIT_PERMISSION_0;
        $staff_result['job_grade_edit_permission_for_fbid'] =
            $jobGradeEditPermission == StaffInfo::JOB_GRADE_EDIT_PERMISSION_2
            ||
            $jobGradeEditPermission == StaffInfo::JOB_GRADE_EDIT_PERMISSION_1 && $staffs[$fbid]['job_title_grade_v2'] >= $staffs[$staffId]['job_title_grade_v2']
                ? $jobGradeEditPermission : StaffInfo::JOB_GRADE_EDIT_PERMISSION_0;      // 普通权限 编辑前后都不低于操作人职级
        $staff_result['config_grade_list'] = [];
        // 员工可编辑职位等级列表 有编辑普通权限 和 特殊权限
        if ($staffs && isset($staffs[$staffId]) && $jobGradeEditPermission) {
            $staff_result['config_grade_list'] = Staff::getHrJobDepartmentRelationById($staffs[$staffId]['node_department_id'], $staffs[$staffId]['job_title']);
            $staff_result['config_grade_list'] = Staff::filterGradeListByStaff($staff_result['config_grade_list'], $staffs[$fbid]['job_title_grade_v2'], $jobGradeEditPermission);
        }

        return $staff_result;
    }

    public static function hasGradePermissionTab($staffId, $fbid)
    {
        $hidden_job_grade_staffs = StaffService::getInstance()->getCEmployee();
        if (in_array($staffId, $hidden_job_grade_staffs)) {
            return 0;
        }
        $hris_staff_super_admin = [];
        $_tmp_hris_staff_super_admin = SettingEnv::find()->where(['code' => 'hris_staff_super_admin'])->one();
        if(!empty($_tmp_hris_staff_super_admin) && $_tmp_hris_staff_super_admin->set_val){
            $hris_staff_super_admin = explode(',',$_tmp_hris_staff_super_admin->set_val);
        }
        $items = StaffInfoPosition::find()->select('position_category')->where(['staff_info_id' => $fbid])->column();
        $permission['fbi.role'] = [];
        foreach ($items as $value) {
            $permission['fbi.role'][] = Yii::$app->sysconfig->getRolesFbi()[$value] ?? $value;
        }

        if (in_array('PERSONNEL_MANAGER',
                $permission['fbi.role'])
            ||
            in_array('SUPER_ADMIN',
                $permission['fbi.role'])
            && !in_array($fbid, $hris_staff_super_admin)
        ) {
            // 全部
            return 1;
        }
        $andConditions = [];
        if (count(array_intersect(['ER', 'HRBP', 'PAYROLL_MANAGER', 'TALENT_ACQUISITION', 'HRD'],
            $permission['fbi.role']))
        ) {
            //  管辖范围
            $result = Staff::dominDepartmentsAndStores($fbid);
            $andConditions['node_department_id'] = $result['flash_home_departments'];
            $andConditions['sys_store_id'] = $result['stores'];

        }

        // 所属部门以及下级
        $dept_ids = SysGroupDeptV2::getDeptIdByManagerId($fbid, 0); // 去掉自己所属部门
        $dept_ids = array_unique(array_merge($dept_ids, SysGroupDeptV2::getDeptIdByAssistantId($fbid, 0))); // 去掉自己所属部门
        $dept_ids = array_values($dept_ids);
        if (isset($andConditions) && isset($andConditions['node_department_id'])) {
            $andConditions['node_department_id'] = array_merge($andConditions['node_department_id'], $dept_ids);
        } else {
            $andConditions['node_department_id'] = $dept_ids;
        }
        $next_level_staff_ids = Staff::getNextLevelStaff($fbid);
        $andConditions['staff_info_id'] = $next_level_staff_ids;
        if ($andConditions) {
            $orConditions[] = 'OR';
            foreach ($andConditions as $k => $v) {
                if ($k == 'sys_store_id' && in_array('-2', (array) $v)) {
                    $orConditions[] = ['NOT IN', 'hr_staff_info.' . $k, ['-1']];
                } else {
                    $orConditions[] = ['IN', 'hr_staff_info.' . $k, $v ?? []];
                }
            }

            $query = (new Query())->select('hr_staff_info.*')->from('hr_staff_info');
            $query->where(['hr_staff_info.staff_info_id' => $staffId]);

            if (count($orConditions) >1) {
                $query->andWhere($orConditions);
                $row = $query->all(Yii::$app->get('r_backyard'));
                if ($row) {
                    // 上面的范围查询可以查到 就不用查白名单了
                    return 1;
                }
            }
        }

        /**
         *
         * 4. 对于不符合上述角色/身份的员工 职位等级白名单
         */
        $result = Staff::gradeWhiteListDepartmentsAndStores($fbid);
        $andConditions = [];
        $andConditions['node_department_id'] = $result['flash_home_departments'];
        $andConditions['sys_store_id'] = $result['stores'];
        if ($andConditions) {
            $orConditions = [];
            $orConditions[] = 'OR';
            foreach ($andConditions as $k => $v) {
                if ($k == 'sys_store_id' && in_array('-2', (array)$v)) {
                    $orConditions[] = ['NOT IN', 'hr_staff_info.' . $k, ['-1']];
                } else {
                    $orConditions[] = ['IN', 'hr_staff_info.' . $k, $v ?? []];
                }
            }
            $query = (new Query())->select('hr_staff_info.*')->from('hr_staff_info');
            $query->where(['hr_staff_info.staff_info_id' => $staffId]);

            if (count($orConditions) >1) {
                $query->andWhere($orConditions);
                $row = $query->all(Yii::$app->get('r_backyard'));
                return $row ? 1 : 0;
            }
        }
        return 0;
    }

    public static function staffsManager($params)
    {
        $manageType = $params['type'];
        $depeartmentId = $params['sys_department_id'];
        $storeId = $params['sys_store_id'];
        $roles = $params['positions'] ?? [];
        $name = $params['name'];

        $result = [];
        if (!empty($name)) {
            // 搜索
            $result = StaffInfo::find()
                ->select(['staff_info_id', 'name', 'state'])
                ->where(['OR',
                    ['LIKE', 'hr_staff_info.name', $name],
                    ['LIKE', 'hr_staff_info.staff_info_id', $name],
                    // ['LIKE', 'hr_staff_info.emp_id', $name],
                    // ['LIKE', 'hr_staff_info.name_en', $name],
                ])
                ->andWhere(['formal' => 1])
                ->andWhere(['state' => 1])
                ->andWhere(['is_sub_staff' => 0])
                ->orderBy(['staff_info_id' => SORT_ASC])
                ->limit(10)
                ->asArray()
                ->all(Yii::$app->get('r_backyard'));
        }

        if ($manageType != 'direct') {
            return $result ?? [];
        }

        if (empty($depeartmentId) || empty($storeId)) {
            return [];
        }

        $default = Staff::getDefaultManager($depeartmentId, $storeId, $roles);
        $result = array_filter($result, function ($row) use ($default) {
            if (!empty($default)) {
                return $default['staff_info_id'] != $row['staff_info_id'];
            }
            return true;
        });

        if (!empty($default)) {
            $default['staff_info_id'] = (string) ($default['staff_info_id'] ?? '');
            array_unshift($result, $default);
        }
        Yii::$app->logger->write_log('staffsManager 参数：' . json_encode($params) .';结果:'. json_encode($result), 'info');

        return  $result;
    }


    public static function syncItems($post, $fbid)
    {
        $staffInfoId = $fbid;
        $attrs = [];
        foreach (['identity', 'mobile', 'bank_no', 'personal_email', 'bank_type', 'nick_name'] as $attr) {
            if (!empty($post[$attr])) {
                $attrs[$attr] = $post[$attr];
            }
        }

        if (isset($post['personal_email']) && !$post['personal_email']) {
            $attrs['personal_email'] = '';
        }

        if (isset($post['nick_name']) && !$post['nick_name']) {
            $attrs['nick_name'] = '';
        }

        if (isset($post['identity']) && !empty($post['identity'])) {
            if (YII_COUNTRY == 'TH') {
                if (!preg_match("/(^[A-Za-z0-9]{8,13}$)|(^[A-Za-z0-9]{18}$)/u", $post['identity'])) {
                    return ['The identity is invalid'];
                }
            } elseif (YII_COUNTRY == 'ID') {
                if (!preg_match("/(^[A-Za-z0-9_\-]{8,16}$)/u", $post['identity'])) {
                    return ['identity', ' id_staff_identity_length_error'];
                }
            } elseif (YII_COUNTRY == 'PH') {
                if (!preg_match("/(^[A-Za-z0-9_\-]{8,30}$)/u", $post['identity'])) {
                    return ['identity', 'PH_staff_identity_length_error_2'];
                }
            } elseif (YII_COUNTRY == 'LA') {
                if (!preg_match("/(^[A-Za-z0-9_\-]{8,15}$)/u", $post['identity'])) {
                    return ['identity', 'LA_staff_identity_length_error_2'];
                }
            } else {
                if (!preg_match("/(^[A-Za-z0-9_\-]{1,30}$)/u", $post['identity'])) {
                    return ['identity', 'staff_identity_length_error_2'];
                }
            }
        }

        if (!empty($attrs['mobile'])) {
            if (strtoupper(YII_COUNTRY) == 'PH') {
                // 菲律宾
                $attrs['mobile'] = str_pad($attrs['mobile'], 11, "0", STR_PAD_LEFT);
            } else if (strtoupper(YII_COUNTRY) == 'LA' || strtoupper(YII_COUNTRY) == 'MY') {
                // 老挝
                if (strpos($attrs['mobile'], '0') !== 0) {
                    $pad_len = strlen($attrs['mobile']) == 9 ? 10 : 11;
                    $attrs['mobile'] = str_pad($attrs['mobile'], $pad_len, "0", STR_PAD_LEFT);
                }
            }
        }

        if (empty($attrs)) {
            return ['miss params'];
        }

        if (($res = Staff::updateItems($staffInfoId, $attrs, $staffInfoId)) && $res !== true) {
            return $res;
        }
        Yii::$app->logger->write_log('ActionSyscItem修改工号信息,工号:' . $staffInfoId . ';工号信息:' . json_encode($attrs), 'info');

        return $staffInfoId;
    }

    /**
     *
     * @param $staffId
     * @param $fbid
     * @return bool
     * @throws ValidationException|\yii\base\InvalidConfigException
     *
     */
    public static function resetPassword($staffId, $fbid, $lang = 'en')
    {
        $staff_info_id = $staffId ?? -1;
        $model = StaffInfo::find()->where(['staff_info_id' => $staff_info_id])->andWhere(['state' => 1])->one(Yii::$app->get('r_backyard'));
        if (!$model) {
            throw new ValidationException(Yii::$app->lang->get('not_job_in_system'));
        }
        $company_type = StaffService::getInstance()->getMsCompanyType($model->contract_company_id);

        $result = Yii::$app->jrpc->resetStaffPassword([
            'staff_info_id' => $staff_info_id,
            'company_type'  => $company_type,
            'lang'          => $lang,
        ]);

        Yii::$app->logger->write_log('actionResetPassword 结果：'.json_encode($result, JSON_UNESCAPED_UNICODE).';工号:'.$staff_info_id . ' lang:'.$lang,'info');
        if($result == 1)
        {
            $staff_info = Staff::view($staff_info_id) ?? [];
            $before = $staff_info;
            $after = $staff_info;
            $after['reset_pwd'] = '-';
            $log = new HrOperateLogs();
            $log->operater = $fbid;
            $log->before = json_encode(['body' => $before], JSON_UNESCAPED_UNICODE);
            $log->after = json_encode(['body' => $after], JSON_UNESCAPED_UNICODE);
            $log->type = 'staff';
            $log->staff_info_id = $staff_info_id;
            $log->save();

            return true;
        }

        return false;
    }

    /**
     * @description 获取阻止原因翻译Key值
     * @param $id
     * @return string
     */
    public static function getPaymentRemark($id): string
    {
        //连续旷工三天及以上=>连续旷工
        if ($id == 8) {
            return 'stay_away_from_work';
        }

        return 'payment_markup_' . $id;
    }
}
