<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace app\commands;

use app\modules\v1\business\Staff;
use app\services\base\SettingEnvService;
use Yii;
use app\models\manage\HrEmails;
use app\models\manage\StaffInfo;
use app\models\manage\StaffItems;
use PHPExcel_IOFactory;
use app\models\manage\BaseStaffInfo;
use yii\swiftmailer\Mailer;
use app\models\backyard\SettingEnv;

/**
 * This command echoes the first argument that you have entered.
 *
 * This command is provided as an example for you to learn how to create console commands.
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 *
 */
class EmailController extends ConsoleController
{
    /*===============定时任务=================================*/
    //发送入职离职员工信息 0 21 * * *
    public function actionStaffHireLeave()
    {
        try {
            $staffs = StaffInfo::find()->where(['or', ['DATE(hire_date)' => date('Y-m-d')], ['DATE(leave_date)' => date('Y-m-d')]])->all();
            foreach ($staffs as $staff) {
                if (empty($staff->email)) {
                    continue;
                }
                $hireLeave[] = [
                    'Employee No.' => $staff->staff_info_id,
                    'Name' => $staff->name,
                    'Work Status' => $staff->state == 1 ? 'On the job' : 'Resignation',
                    'Email' => $staff->email,
                ];
                $this->echo('员工工号:'.$staff->staff_info_id.',在职状态:'.$staff->state);
            }

            if (!isset($hireLeave)) {
                return;
            }

            $mail = Yii::$app->mailer->compose()
                ->setSubject(YII_COUNTRY .'-[HRIS]Please open/close the mailbox in time！' . date('y-m-d'))
                ->setHtmlBody('<p><strong>Hi,All.</strong></p>
<p>Among the newly established/edited resigned employees in HR today, please see the attachment for the list of employees who need to open/close their email addresses. Please timely update the email status and inform HR.</p>
<p><srong>Thanks，</strong></br>
    Flash Express Team</p>');

            
            $emails = SettingEnvService::getInstance()->getSetVal('hris_entry_email_it', ',');
            if (empty($emails)) {
                Yii::$app->logger->write_log('actionStaffHireLeave-hris_entry_email_it 邮箱配置为空，请联系产品。');
                return;
            }

            
            array_unshift($hireLeave, ['Employee No. ', 'Name', 'Work Status', 'Email']);
            $upload_oss_result = Yii::$app->csv->filePut_v2($hireLeave, 'open_close_email_' . date('Ymd').'.csv');
            $leaveFile = $upload_oss_result['object_url'];

            $mail->attach($leaveFile);
            $mail->setTo($emails);
            $result = $mail->send();
            $this->echo('发送数据:'.json_encode($hireLeave));
            $this->echo('end');
        } catch (\Exception $e) {
            $this->echo('定时任务，发送入职离职员工信息，可能出现的问题' . $e->getMessage().';行号:'.$e->getLine());
            Yii::$app->logger->write_log('定时任务，发送入职离职员工信息，可能出现的问题' . $e->getMessage().';行号:'.$e->getLine());
        }
        $this->echo('end');
    }

    //直线上级离职发邮件 0 19 * * *
    public function actionManagerLeave($send = false)
    {

        try {
            $date = Yii::$app->dateTh->sub(new \DateInterval('P2D'))->format('Y-m-d 18:00:00');
            $sql = 'SELECT *
                FROM
                    (SELECT *
                     FROM `hr_staff_logs`
                     WHERE created_at > "' . $date . '"
                     ORDER BY created_at DESC) AS c
                WHERE state = 2
                GROUP BY staff_info_id';
            $all = Yii::$app->get('r_backyard')->createCommand($sql)->queryAll();

            if (empty($all)) {
                $this->echo('manager leave empty');
                return;
            }

            $managerIds = array_column($all, 'staff_info_id');
            $leaveStaffs = StaffInfo::find()->where(['staff_info_id' => $managerIds])->indexBy('staff_info_id')->all();

            if (empty($leaveStaffs)) {
                $this->echo('manager leave empty');
                return;
            }

            $staffInfos = StaffInfo::find()
                ->where(['state' => [1,3],'is_sub_staff'=>0])
                ->andWhere(['manger' => array_keys($leaveStaffs)])
                ->indexBy('staff_info_id')
                ->all();
            $rows[] = [
                'Employee  No.',
                'Employee Name ',
                'Department',
                'Position',
                'Immediate superior No.',
                'Immediate superior',
                'Immediate Department',
                'Position',
                'Work Status',
                'Date',
            ];

            foreach ($staffInfos as $staff) {
                try {
                    $master = $leaveStaffs[$staff->manger];
                    $date = $master->state == 2 ? $master->leave_date : $master->stop_duties_date;
                    $rows[] = [
                        $staff->staff_info_id,
                        $staff->name,
                        Yii::$app->sysconfig->allDepeartments[$staff->sys_department_id] ?? '',
                        Yii::$app->sysconfig->jobTitle[$staff->job_title] ?? '',
                        $master->staff_info_id ?? '',
                        $master->name,
                        Yii::$app->sysconfig->allDepeartments[$master->sys_department_id],
                        Yii::$app->sysconfig->jobTitle[$master->job_title] ?? '',
                        $this->lang->get('state_' . $master->state, '', 'en'),
                        date("Y-m-d",strtotime($date)),
                    ];
                } catch (\Exception $e) {
                    var_dump($e->getMessage());
                }
            }

            if (count($rows) == 1) {
                $this->echo('empty rows');
                return;
            }

            $upload_oss_result = Yii::$app->csv->filePut_v2($rows, 'superior-information' . date('ymd').'.csv');
            $file = $upload_oss_result['object_url'];
            if (YII_COUNTRY == 'VN') {

                $mail = Yii::$app->mailer->compose()
                    ->setSubject('【VN】【HRIS】 Resignation report of immediate  superior！' . date('ymd'))
                    ->setHtmlBody('<p><strong>Hi all,</strong></p>
<p>The immediate supervisor of the employee has been dimissioned . Please update the new information to BI/HCM.</p>
<p>以下员工的直线上级已经离职，请在BI/HCM及时更新新的直线上级信息.</p>
<p><srong>Thanks，</strong></br>
    Flash Express Team</p>');
            } else if (YII_COUNTRY == 'ID') {

                $mail = Yii::$app->mailer->compose()
                    ->setSubject('【ID】【HRIS】 Resignation report of immediate superior！' . date('ymd'))
                    ->setHtmlBody('<p><strong>Hi all,</strong></p>
<p>The immediate supervisor of the employee has been dimissioned . Please update the new information to BI/HCM.</p>
<p><srong>Thanks，</strong></br>
    Flash Express Team</p>');
            } else {
                $mail = Yii::$app->mailer->compose()
                    ->setSubject('【HRIS】 Resignation report of immediate  superior！' . date('ymd'))
                    ->setHtmlBody('<p><strong>Hi all,</strong></p>
<p>The immediate supervisor of the employee has been dimissioned . Please update the new information to BI/HCM.</p>
<p><srong>Thanks，</strong></br>
    Flash Express Team</p>');
            }

            //使用hcm配置邮箱
            $ManagerLeaveEmail = SettingEnv::find()->where(['code' => 'ManagerLeaveEmail'])->one();
            if(!empty($ManagerLeaveEmail) && $ManagerLeaveEmail->set_val){
                $emails = explode(',',$ManagerLeaveEmail->set_val);
            }
            if(empty($emails)){
                $emails = [
                    '<EMAIL>',
                    '<EMAIL>',
                ];
                if (YII_ENV == 'dev') {
                    $emails = [
                        '<EMAIL>',
                        '<EMAIL>',
                    ];
                }
            }
                

            if ($file ?? false) {
                $mail->attach($file);
            }

            $mail->setTo($emails);
            if ($mail->send()) {
                loggerInfo('send ok');
            } else {
                loggerInfo('send fail');
            }
        } catch (\Exception $e) {
            $this->echo('定时任务，直线上级离职发邮件，可能出现的问题' . $e->getMessage().';行号:'.$e->getLine());
            Yii::$app->logger->write_log('定时任务，直线上级离职发邮件，可能出现的问题' . $e->getMessage().';行号:'.$e->getLine());
        }
    }

    /*========================================================*/

    public function actionHistory()
    {
        foreach (StaffInfo::find()->each(1000) as $staff) {
            if (!stripos($staff->email, '@flashexpress.com') || $staff->is_sub_staff) {
                continue;
            }
            $email = new HrEmails();
            $email->staff_info_id = $staff->staff_info_id;
            $email->state = in_array($staff->state, [1, 3]) ? 0 : 1;
            $email->email = $staff->email;
            if ($email->save()) {
                $this->info('ok');
            }
        }
    }

    public function actionImportEmail() {
        $excelFile = __DIR__.'/data/hr_staff_email2019-09-04.xlsx';
        //读取Excel，xlsx后缀文件用Excel2007，xls后缀用Excel5
        $excelReader = PHPExcel_IOFactory::createReader('Excel2007');
        $excelReader->setReadDataOnly(true);
        //载入文件并获取第一个sheet
        $sheet = $excelReader->load($excelFile)->getSheet(0);
        $total_line = $sheet->getHighestRow();

        for ($row = 2; $row <= $total_line; $row++) {
            $excelData = [];
            for ($column = 'A'; $column <= 'B'; $column++) {
                $excelData[] = trim($sheet->getCell($column . $row)->getValue());
            }
            if(empty($excelData[0]) || empty($excelData[1])) {
                continue;
            }
            $staff_info_id = intval(trim($excelData[0]));
            $email = trim($excelData[1]);
            //$staff = BaseStaffInfo::find()->where(['staff_info_id' => $staff_info_id])->one();
            $staff = StaffInfo::find()->where(['staff_info_id' => $staff_info_id])->one();
            if($staff) {
                if(Yii::$app->backyard_main->createCommand()->update('hr_staff_info', ['email' => $email], 'staff_info_id='.$staff_info_id)->execute()) {
                    $staff_email = HrEmails::find()->where(['staff_info_id' => $staff_info_id])->one();
                    if(!$staff_email) {
                        $staff_email = new HrEmails();
                        $staff_email->staff_info_id =$staff_info_id;
                    }
                    $staff_email->email = $email;
                    $staff_email->state = 1;
                    if($staff_email->save()) {
                        $this->echo($staff_info_id.'---'.$email.' ok');
                    } else {
                        $this->echo($staff_info_id.'---'.$email.' update error');
                    }
                } else {
                    $this->echo($staff_info_id.'---'.$email.' hr_staff_info update error');
                }
            } else {
                $this->echo($staff_info_id.'---未找到工号');
            }
        }
        $this->echo('end');
    }
}
